.page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 32rpx;
  color: #999;
}

/* 评价容器 */
.review-container {
  padding: 20rpx;
}

/* 订单信息 */
.order-info {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.order-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.order-no {
  font-size: 28rpx;
  color: #666;
}

.bundle-tip {
  background: #fff3cd;
  border: 1rpx solid #ffeaa7;
  border-radius: 8rpx;
  padding: 16rpx;
}

.tip-text {
  font-size: 28rpx;
  color: #856404;
}

/* 景区列表 */
.scenic-list {
  margin-bottom: 20rpx;
}

.scenic-item {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 景区信息 */
.scenic-info {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.scenic-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.scenic-details {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.scenic-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.scenic-address {
  font-size: 26rpx;
  color: #666;
}

/* 星级评价 */
.rating-section {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.rating-label {
  font-size: 30rpx;
  color: #333;
  margin-right: 20rpx;
}

.stars {
  display: flex;
  margin-right: 20rpx;
}

.star {
  margin-right: 10rpx;
  cursor: pointer;
}

.star-icon {
  font-size: 40rpx;
  color: #ddd;
  transition: color 0.2s;
}

.star.active .star-icon {
  color: #ffb400;
}

.rating-text {
  font-size: 28rpx;
  color: #666;
}

/* 评价内容 */
.content-section {
  margin-bottom: 20rpx;
}

.content-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.content-input {
  width: 100%;
  min-height: 200rpx;
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 12rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.char-count {
  text-align: right;
  margin-top: 10rpx;
}

.count-text {
  font-size: 24rpx;
  color: #999;
}

/* 提交按钮 */
.submit-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  padding: 20rpx;
  box-shadow: 0 -2rpx 8rpx rgba(0,0,0,0.1);
}

.submit-btn {
  background: #667eea;
  color: white;
  text-align: center;
  padding: 24rpx;
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn.disabled {
  background: #ccc;
  color: #999;
}

.submit-text {
  color: inherit;
}

/* 返回按钮 */
.back-btn {
  position: fixed;
  top: 100rpx;
  left: 20rpx;
  background: rgba(0,0,0,0.5);
  color: white;
  padding: 16rpx 24rpx;
  border-radius: 20rpx;
  z-index: 100;
}

.back-text {
  font-size: 28rpx;
  color: white;
}
