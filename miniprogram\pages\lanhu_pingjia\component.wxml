<view class="page">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 评价内容 -->
  <view wx:else class="review-container">
    <!-- 订单信息 -->
    <view class="order-info">
      <view class="order-header">
        <text class="order-title">订单评价</text>
        <text class="order-no">订单号：{{orderInfo.orderNo}}</text>
      </view>
      <view wx:if="{{isBundle}}" class="bundle-tip">
        <text class="tip-text">组合包订单，请对每个景区分别评价</text>
      </view>
    </view>

    <!-- 景区评价列表 -->
    <view class="scenic-list">
      <view wx:for="{{scenicList}}" wx:key="scenicId" wx:for-index="scenicIndex" class="scenic-item">
        <!-- 景区信息 -->
        <view class="scenic-info">
          <image src="{{item.image || '../../images/lanhu_quanbudingdan/FigmaDDSSlicePNG675e7299676300162f1e556d0ba5d74e.png'}}" class="scenic-image"></image>
          <view class="scenic-details">
            <text class="scenic-name">{{item.title}}</text>
            <text class="scenic-address">{{item.address}}</text>
          </view>
        </view>

        <!-- 星级评价 -->
        <view class="rating-section">
          <text class="rating-label">评分：</text>
          <view class="stars">
            <view wx:for="{{[1,2,3,4,5]}}" wx:key="*this" wx:for-item="star" 
                  class="star {{reviews[scenicIndex].rating >= star ? 'active' : ''}}"
                  data-scenic-index="{{scenicIndex}}" 
                  data-rating="{{star}}" 
                  bindtap="onStarTap">
              <text class="star-icon">★</text>
            </view>
          </view>
          <text class="rating-text">{{reviews[scenicIndex].rating}}分</text>
        </view>

        <!-- 评价内容 -->
        <view class="content-section">
          <text class="content-label">评价内容：</text>
          <textarea 
            class="content-input" 
            placeholder="请输入您对该景区的评价..."
            value="{{reviews[scenicIndex].content}}"
            data-scenic-index="{{scenicIndex}}"
            bindinput="onContentInput"
            maxlength="500"
            show-confirm-bar="{{false}}">
          </textarea>
          <view class="char-count">
            <text class="count-text">{{reviews[scenicIndex].content.length}}/500</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <view class="submit-btn {{submitting ? 'disabled' : ''}}" bindtap="submitReviews">
        <text class="submit-text">{{submitting ? '提交中...' : '提交评价'}}</text>
      </view>
    </view>
  </view>

</view>
