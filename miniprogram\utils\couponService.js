// 门票/优惠券服务模块
const httpService = require('./httpService');

class CouponService {
  constructor() {
    this.cacheKeyPrefix = 'coupons_cache_';
    this.cacheTime = 2 * 60 * 1000; // 2分钟缓存
  }

  // 创建门票
  async createCoupon(couponData) {
    try {
      if (!couponData.orderId) {
        throw new Error('订单ID不能为空');
      }
      if (!couponData.productId) {
        throw new Error('产品ID不能为空');
      }
      if (!couponData.userId) {
        throw new Error('用户ID不能为空');
      }

      console.log('创建门票:', couponData);

      const coupon = await httpService.post('/api/coupons', {
        orderId: couponData.orderId,
        productId: couponData.productId,
        userId: couponData.userId,
        status: couponData.status || 'unactivated',
        code: couponData.code || this.generateCouponCode(),
        name: couponData.name || '景区门票',
        description: couponData.description || '',
        validFrom: couponData.validFrom || new Date().toISOString(),
        usageLimit: couponData.usageLimit || 1,
        usedCount: 0,
        value: couponData.value || 0,
        type: couponData.type || 'ticket'
      }, {
        showLoading: false
      });

      console.log('门票创建成功:', coupon);
      return coupon;
    } catch (error) {
      console.error('创建门票失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 批量创建门票
  async createMultipleCoupons(orderData, quantity) {
    try {
      if (!orderData.orderId || !orderData.productId || !orderData.userId) {
        throw new Error('订单信息不完整');
      }
      if (!quantity || quantity < 1) {
        throw new Error('门票数量必须大于0');
      }

      console.log('批量创建门票:', orderData, quantity);

      const coupons = [];
      const createPromises = [];

      for (let i = 0; i < quantity; i++) {
        const couponData = {
          ...orderData,
          code: this.generateCouponCode(),
          name: `${orderData.productName || '景区门票'} (${i + 1}/${quantity})`
        };

        createPromises.push(this.createCoupon(couponData));
      }

      const results = await Promise.all(createPromises);
      coupons.push(...results);

      console.log(`批量创建门票成功，共创建${coupons.length}张门票`);
      return coupons;
    } catch (error) {
      console.error('批量创建门票失败:', error);
      throw error;
    }
  }

  // 获取用户门票列表
  async getUserCoupons(userId, params = {}) {
    try {
      if (!userId) {
        throw new Error('用户ID不能为空');
      }

      const {
        status,
        type,
        current = 1,
        size = 20
      } = params;

      const queryParams = {
        userId: userId.toString(),
        current: current.toString(),
        size: size.toString()
      };

      if (status) {
        queryParams.status = status;
      }

      if (type) {
        queryParams.type = type;
      }

      const coupons = await httpService.get('/api/coupons/page', queryParams, {
        loadingText: '加载门票列表中...'
      });

      return coupons;
    } catch (error) {
      console.error('获取用户门票列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取门票详情
  async getCouponDetail(couponId) {
    try {
      if (!couponId) {
        throw new Error('门票ID不能为空');
      }

      const coupon = await httpService.get(`/api/coupons/${couponId}`, {}, {
        loadingText: '加载门票详情中...'
      });

      return coupon;
    } catch (error) {
      console.error('获取门票详情失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 激活门票
  async activateCoupon(couponId) {
    try {
      if (!couponId) {
        throw new Error('门票ID不能为空');
      }

      console.log('激活门票:', couponId);

      const result = await httpService.post(`/api/coupons/${couponId}/activate`,{
        loadingText: '激活门票中...'
      });

      console.log('门票激活成功:', result);
      return result;
    } catch (error) {
      console.error('激活门票失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 生成门票编码
  generateCouponCode() {
    const timestamp = Date.now().toString();
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const prefix = 'TKT'; // Ticket前缀
    return `${prefix}${timestamp.slice(-8)}${random}`;
  }

  // 计算有效期截止时间
  calculateValidTo(hours) {
    const now = new Date();
    const validTo = new Date(now.getTime() + hours * 60 * 60 * 1000);
    return validTo.toISOString();
  }

  // 验证门票编码
  async validateCouponCode(code) {
    try {
      if (!code) {
        throw new Error('门票编码不能为空');
      }

      const result = await httpService.get(`/api/coupons/validate/${code}`, {}, {
        loadingText: '验证门票中...'
      });

      return result;
    } catch (error) {
      console.error('验证门票编码失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取订单相关的门票
  async getOrderCoupons(orderId) {
    try {
      if (!orderId) {
        throw new Error('订单ID不能为空');
      }

      const coupons = await httpService.get(`/api/coupons/order/${orderId}`, {}, {
        loadingText: '加载中...'
      });

      return coupons;
    } catch (error) {
      console.error('获取订单门票失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 查询用户在指定景区的未激活门票
  async getUnactivatedCouponsByScenic(userId, scenicId) {
    try {
      if (!userId) {
        throw new Error('用户ID不能为空');
      }
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('查询未激活门票:', userId, scenicId);

      const coupons = await httpService.get('/api/coupons/unactivated', {
        userId: userId.toString(),
        scenicId: scenicId.toString()
      }, {
        showLoading: false
      });

      console.log('查询到未激活门票:', coupons);
      return coupons;
    } catch (error) {
      console.error('查询未激活门票失败:', error);
      throw error;
    }
  }

  // 检查用户在指定景区是否有未激活门票
  async hasUnactivatedCoupons(userId, scenicId) {
    try {
      if (!userId) {
        throw new Error('用户ID不能为空');
      }
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('检查是否有未激活门票:', userId, scenicId);

      const result = await httpService.get('/api/coupons/has-unactivated', {
        userId: userId.toString(),
        scenicId: scenicId.toString()
      }, {
        showLoading: false
      });

      console.log('未激活门票检查结果:', result);
      return result;
    } catch (error) {
      console.error('检查未激活门票失败:', error);
      throw error;
    }
  }

  // 查询用户在指定景区的已激活门票
  async getActivatedCouponsByScenic(userId, scenicId) {
    try {
      if (!userId) {
        throw new Error('用户ID不能为空');
      }
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('查询已激活门票:', userId, scenicId);

      const coupons = await httpService.get('/api/coupons/activated', {
        userId: userId.toString(),
        scenicId: scenicId.toString()
      }, {
        showLoading: false
      });

      console.log('查询到已激活门票:', coupons);
      return coupons;
    } catch (error) {
      console.error('查询已激活门票失败:', error);
      throw error;
    }
  }

  // 检查用户在指定景区是否有已激活门票
  async hasActivatedCoupons(userId, scenicId) {
    try {
      if (!userId) {
        throw new Error('用户ID不能为空');
      }
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('检查是否有已激活门票:', userId, scenicId);

      const result = await httpService.get('/api/coupons/has-activated', {
        userId: userId.toString(),
        scenicId: scenicId.toString()
      }, {
        showLoading: false
      });

      console.log('已激活门票检查结果:', result);
      return result;
    } catch (error) {
      console.error('检查已激活门票失败:', error);
      throw error;
    }
  }

  // 标记卡券在指定景区已使用
  async markCouponAsUsed(couponId, scenicId) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('标记卡券已使用:', couponId, scenicId);

      const result = await httpService.put('/api/coupon-usage-records/mark-used', {
        couponId: couponId,
        scenicId: scenicId
      }, {
        showLoading: false
      });

      console.log('标记卡券已使用成功:', result);
      return result;
    } catch (error) {
      console.error('标记卡券已使用失败:', error);
      throw error;
    }
  }

  // 批量创建组合包卡券使用记录
  async createBundleUsageRecords(couponId, userId, scenicIds) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }
      if (!userId) {
        throw new Error('用户ID不能为空');
      }
      if (!scenicIds || !Array.isArray(scenicIds) || scenicIds.length === 0) {
        throw new Error('景区ID列表不能为空');
      }

      console.log('批量创建组合包使用记录:', couponId, userId, scenicIds);

      const result = await httpService.post('/api/coupon-usage-records/batch/bundle', {
        couponId: couponId,
        userId: userId,
        scenicIds: scenicIds
      }, {
        showLoading: false
      });

      console.log('批量创建组合包使用记录成功:', result);
      return result;
    } catch (error) {
      console.error('批量创建组合包使用记录失败:', error);
      throw error;
    }
  }

  // 激活组合包景区
  async activateBundleScenic(couponId, userId, currentScenicId, allScenicIds) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }
      if (!userId) {
        throw new Error('用户ID不能为空');
      }
      if (!currentScenicId) {
        throw new Error('当前景区ID不能为空');
      }
      if (!allScenicIds || !Array.isArray(allScenicIds) || allScenicIds.length === 0) {
        throw new Error('所有景区ID列表不能为空');
      }

      console.log('激活组合包景区:', couponId, userId, currentScenicId, allScenicIds);

      const result = await httpService.post('/api/coupon-usage-records/activate-bundle-scenic', {
        couponId: couponId,
        userId: userId,
        currentScenicId: currentScenicId,
        allScenicIds: allScenicIds
      }, {
        showLoading: false
      });

      console.log('激活组合包景区成功:', result);
      return result;
    } catch (error) {
      console.error('激活组合包景区失败:', error);
      throw error;
    }
  }

  // 检查卡券在指定景区是否已使用
  async checkCouponUsed(couponId, scenicId) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('检查卡券使用状态:', couponId, scenicId);

      const result = await httpService.get('/api/coupon-usage-records/check-used', {
        couponId: couponId.toString(),
        scenicId: scenicId.toString()
      }, {
        showLoading: false
      });

      console.log('卡券使用状态检查结果:', result);
      return result;
    } catch (error) {
      console.error('检查卡券使用状态失败:', error);
      throw error;
    }
  }

  // 检查卡券在指定景区的激活状态
  async checkScenicActivationStatus(couponId, scenicId) {
    try {
      if (!couponId) {
        throw new Error('卡券ID不能为空');
      }
      if (!scenicId) {
        throw new Error('景区ID不能为空');
      }

      console.log('检查景区激活状态:', couponId, scenicId);

      const result = await httpService.get('/api/coupon-usage-records/check-activation-status', {
        couponId: couponId.toString(),
        scenicId: scenicId.toString()
      }, {
        showLoading: false
      });

      console.log('景区激活状态检查结果:', result);
      return result;
    } catch (error) {
      console.error('检查景区激活状态失败:', error);
      throw error;
    }
  }

  // 根据卡券编码获取使用记录
  async getCouponUsageRecordsByCode(couponCode) {
    try {
      if (!couponCode) {
        throw new Error('卡券编码不能为空');
      }

      console.log('获取卡券使用记录:', couponCode);

      const result = await httpService.get(`/api/coupon-usage-records/coupon-code/${couponCode}`, {}, {
        showLoading: false
      });

      console.log('卡券使用记录获取成功:', result);
      return result;
    } catch (error) {
      console.error('获取卡券使用记录失败:', error);
      throw error;
    }
  }

  // 获取产品详情（用于判断产品类型）
  async getProductDetail(productId) {
    try {
      if (!productId) {
        throw new Error('产品ID不能为空');
      }

      console.log('获取产品详情:', productId);

      const product = await httpService.get(`/api/products/${productId}`, {}, {
        showLoading: false
      });

      console.log('产品详情获取成功:', product);
      return product;
    } catch (error) {
      console.error('获取产品详情失败:', error);
      throw error;
    }
  }

  // 清除缓存
  clearCache() {
    try {
      const keys = wx.getStorageInfoSync().keys;
      keys.forEach(key => {
        if (key.startsWith(this.cacheKeyPrefix)) {
          wx.removeStorageSync(key);
        }
      });
      console.log('门票缓存已清除');
    } catch (error) {
      console.error('清除门票缓存失败:', error);
    }
  }

  // 获取分享卡券信息
  async getShareCouponInfo(code) {
    try {
      if (!code) {
        throw new Error('卡券编码不能为空');
      }

      console.log('获取分享卡券信息:', code);

      const result = await httpService.get(`/api/coupons/share/${code}`, {}, {
        showLoading: true
      });

      console.log('分享卡券信息获取结果:', result);
      return result;
    } catch (error) {
      console.error('获取分享卡券信息失败:', error);
      throw error;
    }
  }

  // 领取分享的卡券
  async claimSharedCoupon(code, userId) {
    try {
      if (!code) {
        throw new Error('卡券编码不能为空');
      }
      if (!userId) {
        throw new Error('用户ID不能为空');
      }

      console.log('领取分享卡券:', code, userId);

      const result = await httpService.post(`/api/coupons/claim/${code}?userId=${userId}`, {}, {
        showLoading: true
      });

      console.log('领取分享卡券结果:', result);
      return result;
    } catch (error) {
      console.error('领取分享卡券失败:', error);
      throw error;
    }
  }
}

// 创建单例实例
const couponService = new CouponService();

module.exports = couponService;
