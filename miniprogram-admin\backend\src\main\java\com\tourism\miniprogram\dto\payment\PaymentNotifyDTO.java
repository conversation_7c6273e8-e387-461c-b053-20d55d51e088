package com.tourism.miniprogram.dto.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 支付通知DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Data
@ApiModel(value = "支付通知", description = "拉卡拉支付结果通知参数")
public class PaymentNotifyDTO {

    @ApiModelProperty(value = "商户号")
    private String merchantNo;

    @ApiModelProperty(value = "终端号")
    private String termNo;

    @ApiModelProperty(value = "商户订单号")
    private String outTradeNo;

    @ApiModelProperty(value = "拉卡拉订单号")
    private String lakalaOrderNo;

    @ApiModelProperty(value = "第三方订单号（微信订单号）")
    private String thirdOrderNo;

    @ApiModelProperty(value = "交易状态（SUCCESS-成功，FAILED-失败）")
    private String tradeStatus;

    @ApiModelProperty(value = "订单金额（元）")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "实际支付金额（元）")
    private BigDecimal receiptAmount;

    @ApiModelProperty(value = "交易类型")
    private String transType;

    @ApiModelProperty(value = "账户类型")
    private String accountType;

    @ApiModelProperty(value = "支付完成时间")
    private String payTime;

    @ApiModelProperty(value = "通知时间")
    private String notifyTime;

    @ApiModelProperty(value = "签名")
    private String sign;

    @ApiModelProperty(value = "错误码")
    private String errorCode;

    @ApiModelProperty(value = "错误描述")
    private String errorMsg;

    // 拉卡拉签名验证相关字段
    @ApiModelProperty(value = "拉卡拉分配的appId")
    private String appId;

    @ApiModelProperty(value = "证书序列号")
    private String serialNo;

    @ApiModelProperty(value = "时间戳")
    private String timestamp;

    @ApiModelProperty(value = "随机字符串")
    private String nonceStr;

    @ApiModelProperty(value = "签名")
    private String signature;

    @ApiModelProperty(value = "响应体内容")
    private String responseBody;

    /**
     * 判断支付是否成功
     */
    public boolean isPaymentSuccess() {
        return "SUCCESS".equals(tradeStatus);
    }

    /**
     * 判断支付是否失败
     */
    public boolean isPaymentFailed() {
        return "FAILED".equals(tradeStatus);
    }

    /**
     * 获取支付完成时间（转换为LocalDateTime）
     */
    public LocalDateTime getPayTimeAsLocalDateTime() {
        if (payTime == null || payTime.trim().isEmpty()) {
            return null;
        }
        try {
            // 假设时间格式为：yyyy-MM-dd HH:mm:ss
            return LocalDateTime.parse(payTime.replace(" ", "T"));
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 获取通知时间（转换为LocalDateTime）
     */
    public LocalDateTime getNotifyTimeAsLocalDateTime() {
        if (notifyTime == null || notifyTime.trim().isEmpty()) {
            return null;
        }
        try {
            // 假设时间格式为：yyyy-MM-dd HH:mm:ss
            return LocalDateTime.parse(notifyTime.replace(" ", "T"));
        } catch (Exception e) {
            return null;
        }
    }
}
