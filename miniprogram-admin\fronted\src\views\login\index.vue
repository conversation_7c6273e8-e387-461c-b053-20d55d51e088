<template>
  <div class="login-container">
    <div class="login-form">
      <div class="login-header">
        <h2>旅游讲解小程序</h2>
        <p>管理后台</p>
      </div>
      
      <el-card class="login-card">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          class="login-form-content"
          auto-complete="on"
          label-position="left"
        >
          <div class="title-container">
            <h3 class="title">系统登录</h3>
          </div>

          <el-form-item prop="username">
            <span class="svg-container">
              <el-icon><User /></el-icon>
            </span>
            <el-input
              ref="username"
              v-model="loginForm.username"
              placeholder="用户名"
              name="username"
              type="text"
              tabindex="1"
              auto-complete="on"
            />
          </el-form-item>

          <el-form-item prop="password">
            <span class="svg-container">
              <el-icon><Lock /></el-icon>
            </span>
            <el-input
              :key="passwordType"
              ref="password"
              v-model="loginForm.password"
              :type="passwordType"
              placeholder="密码"
              name="password"
              tabindex="2"
              auto-complete="on"
              @keyup.enter="handleLogin"
            />
            <span class="show-pwd" @click="showPwd">
              <el-icon>
                <component :is="passwordType === 'password' ? 'View' : 'Hide'" />
              </el-icon>
            </span>
          </el-form-item>

          <el-button
            :loading="loading"
            type="primary"
            style="width:100%;margin-bottom:30px;"
            @click.prevent="handleLogin"
          >
            登录
          </el-button>

          <div class="tips">
            <span style="margin-right:20px;">用户名: admin</span>
            <span>密码: 123456</span>
          </div>
        </el-form>
      </el-card>
      
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, nextTick, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { testApi } from '@/api/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

const loginFormRef = ref()
const username = ref()
const password = ref()

const loginForm = reactive({
  username: 'admin',
  password: '123456'
})

const loginRules = {
  username: [{ required: true, trigger: 'blur', message: '请输入用户名' }],
  password: [{ required: true, trigger: 'blur', message: '请输入密码' }]
}

const loading = ref(false)
const passwordType = ref('password')
const redirect = ref(undefined)
const otherQuery = ref({})

// 显示/隐藏密码
const showPwd = () => {
  if (passwordType.value === 'password') {
    passwordType.value = ''
  } else {
    passwordType.value = 'password'
  }
  nextTick(() => {
    password.value.focus()
  })
}

// 处理登录
const handleLogin = () => {
  loginFormRef.value.validate(async (valid) => {
    if (valid) {
      loading.value = true
      try {
        // 模拟登录过程
        if (loginForm.username === 'admin' && loginForm.password === '123456') {
          // 生成模拟token
          const token = 'mock_token_' + Date.now()
          userStore.setToken(token)
          
          ElMessage.success('登录成功')
          
          // 跳转到目标页面
          const targetPath = redirect.value || '/dashboard'
          router.push({ path: targetPath, query: otherQuery.value })
        } else {
          ElMessage.error('用户名或密码错误')
        }
      } catch (error) {
        ElMessage.error('登录失败')
      } finally {
        loading.value = false
      }
    } else {
      console.log('表单验证失败')
      return false
    }
  })
}

// 测试后端连接
const testConnection = async () => {
  try {
    const response = await testApi()
    if (response.code === 200) {
      ElMessage.success('后端连接正常')
    }
  } catch (error) {
    ElMessage.error('后端连接失败，请检查后端服务是否启动')
  }
}

// 获取重定向信息
const getOtherQuery = (query) => {
  return Object.keys(query).reduce((acc, cur) => {
    if (cur !== 'redirect') {
      acc[cur] = query[cur]
    }
    return acc
  }, {})
}

onMounted(() => {
  if (route.query) {
    redirect.value = route.query.redirect
    otherQuery.value = getOtherQuery(route.query)
  }
})
</script>

<style lang="scss" scoped>
.login-container {
  min-height: 100vh;
  width: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-form {
  width: 100%;
  max-width: 520px;
  
  .login-header {
    text-align: center;
    margin-bottom: 30px;
    color: white;
    
    h2 {
      font-size: 28px;
      margin: 0 0 10px;
      font-weight: bold;
    }
    
    p {
      font-size: 16px;
      margin: 0;
      opacity: 0.9;
    }
  }
  
  .login-card {
    margin-bottom: 20px;
    
    .login-form-content {
      position: relative;
      width: 100%;
      max-width: 100%;
      padding: 20px 35px 0;
      margin: 0 auto;
      overflow: hidden;
      
      .title-container {
        position: relative;
        
        .title {
          font-size: 26px;
          color: #409EFF;
          margin: 0px auto 40px auto;
          text-align: center;
          font-weight: bold;
        }
      }
      
      .el-form-item {
        border: 1px solid rgba(255, 255, 255, 0.1);
        background: rgba(0, 0, 0, 0.1);
        border-radius: 5px;
        color: #454545;
        
        .el-input {
          display: inline-block;
          height: 47px;
          width: 85%;
          
          :deep(.el-input__wrapper) {
            background: transparent;
            border: 0px;
            border-radius: 0px;
            padding: 12px 5px 12px 15px;
            color: #fff;
            height: 47px;
            caret-color: #fff;
            box-shadow: none;
            
            &.is-focus {
              box-shadow: none;
            }
          }
          
          :deep(.el-input__inner) {
            background: transparent;
            border: 0px;
            border-radius: 0px;
            padding: 12px 5px 12px 15px;
            color: #fff;
            height: 47px;
            caret-color: #fff;
            
            &:-webkit-autofill {
              box-shadow: 0 0 0px 1000px #2d3a4b inset !important;
              -webkit-text-fill-color: #fff !important;
            }
          }
        }
        
        .svg-container {
          padding: 6px 5px 6px 15px;
          color: #889aa4;
          vertical-align: middle;
          width: 30px;
          display: inline-block;
        }
        
        .show-pwd {
          position: absolute;
          right: 10px;
          top: 7px;
          font-size: 16px;
          color: #889aa4;
          cursor: pointer;
          user-select: none;
        }
      }
      
      .tips {
        font-size: 14px;
        color: #fff;
        margin-bottom: 10px;
        text-align: center;
        
        span {
          &:first-of-type {
            margin-right: 16px;
          }
        }
      }
    }
  }
  
  .wechat-info {
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      color: #409EFF;
      font-weight: bold;
    }
    
    p {
      margin: 10px 0;
      color: #606266;
      line-height: 1.6;
    }
  }
}

@media (max-width: 768px) {
  .login-container {
    padding: 10px;
  }
  
  .login-form {
    .login-header {
      h2 {
        font-size: 24px;
      }
      
      p {
        font-size: 14px;
      }
    }
    
    .login-card .login-form-content {
      padding: 20px 20px 0;
    }
  }
}
</style>
