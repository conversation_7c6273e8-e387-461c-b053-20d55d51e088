package com.tourism.miniprogram.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 拉卡拉支付配置类
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "lakala.payment")
public class LakalaPaymentConfig {

    /**
     * 拉卡拉分配的appId
     */
    private String appId;

    /**
     * 证书序列号
     */
    private String serialNo;

    /**
     * 商户号
     */
    private String merchantNo;

    /**
     * 终端号
     */
    private String termNo;

    /**
     * API地址
     */
    private String apiUrl;



    /**
     * 回调通知地址
     */
    private String notifyUrl;

    /**
     * 请求超时时间（毫秒）
     */
    private Integer timeout = 30000;

    /**
     * 是否启用拉卡拉支付
     */
    private Boolean enabled = true;

    /**
     * 获取预下单API地址
     */
    public String getPreOrderUrl() {
        return apiUrl + "/api/v2/ec/trade/create";
    }

    /**
     * 获取订单查询API地址
     */
    public String getQueryOrderUrl() {
        return apiUrl + "/api/v2/ec/trade/query";
    }

    /**
     * 获取退款API地址
     */
    public String getRefundUrl() {
        return apiUrl + "/api/v2/ec/trade/refund";
    }

    /**
     * 验证配置是否完整
     */
    public boolean isConfigValid() {
        return enabled != null && enabled
                && appId != null && !appId.trim().isEmpty()
                && serialNo != null && !serialNo.trim().isEmpty()
                && merchantNo != null && !merchantNo.trim().isEmpty()
                && termNo != null && !termNo.trim().isEmpty()
                && apiUrl != null && !apiUrl.trim().isEmpty()
                && notifyUrl != null && !notifyUrl.trim().isEmpty();
    }
}
