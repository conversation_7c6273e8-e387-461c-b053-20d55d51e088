/* 页面容器 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 标签栏 */
.tab-bar {
  background-color: #fff;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #e5e5e5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-container {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 10rpx;
  position: relative;
}

.tab-item.active .tab-text {
  color: #007aff;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #007aff;
  border-radius: 2rpx;
}

.tab-text {
  font-size: 28rpx;
  color: #333;
}

/* 订单列表 */
.order-list {
  padding: 20rpx;
}

.order-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 订单头部 */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.order-no {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.order-status {
  font-size: 26rpx;
  color: #007aff;
  font-weight: bold;
}

/* 订单内容 */
.order-content {
  display: flex;
  margin-bottom: 30rpx;
}

.product-image {
  width: 130rpx;
  height: 100rpx;
  border-radius: 12rpx;
  margin-top: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.order-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.product-name {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.order-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 10rpx;
}

.scenic-address {
  margin-bottom: 20rpx;
}

.address-text {
  font-size: 24rpx;
  color: #666;
}

.order-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.item-count {
  font-size: 24rpx;
  color: #666;
}

.price-info {
  display: flex;
  align-items: baseline;
}

.price-label {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.price-symbol {
  font-size: 24rpx;
  color: #ff4444;
  margin-right: 4rpx;
}

.price-amount {
  font-size: 32rpx;
  color: #ff4444;
  font-weight: bold;
}

/* 订单操作按钮 */
.order-actions {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 20rpx;
}

.action-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.action-btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
  background-color: #fff;
}

.action-btn.primary-btn {
  background-color: #007aff;
  border-color: #007aff;
}

.action-btn.primary-btn .btn-text {
  color: #fff;
}

.action-btn.secondary-btn {
  border-color: #007aff;
}

.action-btn.secondary-btn .btn-text {
  color: #007aff;
}

.action-btn.cancel-btn {
  border-color: #ff4444;
}

.action-btn.cancel-btn .btn-text {
  color: #ff4444;
}

.action-btn.refund-btn {
  border-color: #ff9500;
  background-color: #ffffff;
}

.action-btn.refund-btn .btn-text {
  color: #ff9100;
}

.btn-text {
  font-size: 26rpx;
  color: #333;
}

/* 空状态 */
.empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 暂无订单样式 */
.block_2 {
  width: 750rpx;
  height: 1362rpx;
  margin-top: 1200rpx;
  display: flex;
  flex-direction: column;
}
.image_3 {
  width: 332rpx;
  height: 274rpx;
  margin: 250rpx 0 0 208rpx;
}
.group_2 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 804rpx 0 26rpx 232rpx;
}
