package com.tourism.miniprogram.config;

import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * RestTemplate配置类
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Configuration
public class RestTemplateConfig {

    /**
     * 配置RestTemplate
     */
    @Bean
    public RestTemplate restTemplate() {
        // 创建HttpClient
        HttpClient httpClient = HttpClientBuilder.create()
                .setMaxConnTotal(200) // 最大连接数
                .setMaxConnPerRoute(50) // 每个路由的最大连接数
                .build();

        // 创建HttpComponentsClientHttpRequestFactory
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory(httpClient);
        factory.setConnectTimeout(10000); // 连接超时时间：10秒
        factory.setReadTimeout(30000); // 读取超时时间：30秒

        // 创建RestTemplate
        return new RestTemplate(factory);
    }
}
