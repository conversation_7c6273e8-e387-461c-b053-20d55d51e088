.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.group_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.group_2 {
  width: 402rpx;
  height: 46rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 22rpx 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 12rpx;
}
.text_1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.group_3 {
  width: 750rpx;
  height: 1454rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 110rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.text_2 {
  width: 140rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 34rpx 0 0 30rpx;
}
.text-wrapper_2 {
  background-color: rgba(255,255,255,1.000000);
  height: 110rpx;
  margin-top: 2rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.text_3 {
  width: 168rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 34rpx 0 0 30rpx;
}
.text-wrapper_3 {
  background-color: rgba(255,255,255,1.000000);
  height: 242rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  width: 750rpx;
}
.text_4 {
  width: 196rpx;
  height: 40rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.400000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 28rpx;
  margin: 34rpx 0 0 30rpx;
}
.text-wrapper_4 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 200rpx;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  width: 690rpx;
  margin: 726rpx 0 0 30rpx;
}
.text_5 {
  width: 128rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 34rpx 0 0 282rpx;
}
.box_1 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 86rpx 0 30rpx 232rpx;
}

/* 合作申请列表样式 */
.cooperation-list {
  padding: 20rpx;
}

.cooperation-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.cooperation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.cooperation-name {
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
}

.cooperation-time {
  font-size: 24rpx;
  color: #999;
}

.cooperation-phone {
  margin-bottom: 20rpx;
  padding: 16rpx 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
}

.phone-label {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
}

.phone-text {
  font-size: 28rpx;
  color: #333;
  margin-left: 8rpx;
  font-weight: 500;
}

.cooperation-content {
  margin-bottom: 10rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  font-weight: 400;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.empty-icon {
  margin-bottom: 40rpx;
}

.empty-image {
  width: 160rpx;
  height: 160rpx;
  opacity: 0.8;
}

.empty-text {
  margin-bottom: 60rpx;
}

.empty-text text {
  font-size: 32rpx;
  color: rgba(0, 0, 0, 1);
  font-weight: 400;
}

.empty-btn {
  background: linear-gradient(135deg, rgba(51, 49, 49, 0.2) 0%, rgba(32, 4, 4, 0.1) 100%);
  color: rgb(0, 0, 0);
  padding: 24rpx 60rpx;
  border-radius: 50rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: 2rpx solid rgba(255,255,255,0.3);
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.empty-btn:active {
  transform: scale(0.95);
  background: linear-gradient(135deg, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0.2) 100%);
}

/* 浮动添加按钮 */
.floating-add-btn {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.floating-add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.add-btn-text {
  color: white;
  font-size: 48rpx;
  font-weight: 300;
  line-height: 1;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.6);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  background: white;
  color: black;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: black;
}

.modal-close {
  font-size: 44rpx;
  color: rgba(0, 0, 0, 0.8);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:active {
  background: rgba(255,255,255,0.2);
  color: white;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.form-item {
  margin-bottom: 36rpx;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e1e8ed;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background: rgba(255,255,255,0.8);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.form-textarea {
  width: 100%;
  min-height: 180rpx;
  border: 2rpx solid #e1e8ed;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background: rgba(255,255,255,0.8);
  transition: all 0.3s ease;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 40rpx;
  gap: 24rpx;
}

.btn-cancel {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #5a6c7d;
  border: none;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-cancel:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #e8eaf6 0%, #b8c6db 100%);
}

.btn-submit {
  flex: 1;
  height: 88rpx;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
}

.btn-submit:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(102, 126, 234, 0.2);
}

.btn-submit[disabled] {
  background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
  box-shadow: none;
  transform: none;
}