package com.tourism.miniprogram.config;

import com.lkl.laop.sdk.Config;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import org.springframework.core.io.ClassPathResource;

import javax.annotation.PreDestroy;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;

/**
 * 拉卡拉SDK配置类
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Slf4j
@Configuration
public class LakalaSDKConfig {

    @Autowired
    private LakalaPaymentConfig paymentConfig;

    // 固定密钥文件路径
    private static final String PRIVATE_KEY_FILE = "keys/lakala-private-key.pem";
    private static final String PUBLIC_KEY_FILE = "keys/lakala-public-key.cer";

    // 存储创建的临时文件路径，用于清理
    private final List<String> tempFilePaths = new ArrayList<>();

    /**
     * 创建拉卡拉SDK配置Bean
     */
    @Bean
    public Config lakalaConfig() {
        try {
            log.info("初始化拉卡拉SDK配置...");

            // 验证配置完整性
            if (!paymentConfig.isConfigValid()) {
                throw new RuntimeException("拉卡拉支付配置不完整，无法初始化SDK");
            }

            // 获取密钥文件路径
            String privateKeyPath = getKeyFilePath(PRIVATE_KEY_FILE);
            String publicKeyPath = getKeyFilePath(PUBLIC_KEY_FILE);

            log.info("使用密钥文件 - 私钥: {}, 公钥: {}", privateKeyPath, publicKeyPath);

            // 创建SDK配置
            Config config = new Config();

            // 设置基本配置
            config.setAppId(paymentConfig.getAppId());
            config.setSerialNo(paymentConfig.getSerialNo());
            config.setPriKeyPath(privateKeyPath); // 使用临时私钥文件路径
            config.setLklCerPath(publicKeyPath); // 使用临时公钥文件路径

            // 设置API地址
            config.setServerUrl(paymentConfig.getApiUrl());

            // 设置超时时间
            config.setConnectTimeout(paymentConfig.getTimeout());
            config.setReadTimeout(paymentConfig.getTimeout());
            config.setSocketTimeout(paymentConfig.getTimeout());

            log.info("拉卡拉SDK配置初始化完成 - appId: {}, serialNo: {}, apiUrl: {}",
                    paymentConfig.getAppId(), paymentConfig.getSerialNo(), paymentConfig.getApiUrl());

            return config;

        } catch (Exception e) {
            log.error("初始化拉卡拉SDK配置失败", e);
            throw new RuntimeException("初始化拉卡拉SDK配置失败", e);
        }
    }

    /**
     * 获取密钥文件路径
     *
     * @param relativePath 相对于resources的文件路径
     * @return 密钥文件的绝对路径
     * @throws IOException 文件不存在或无法访问
     */
    private String getKeyFilePath(String relativePath) throws IOException {
        // 方案1：尝试从classpath读取资源并创建临时文件
        try {
            ClassPathResource resource = new ClassPathResource(relativePath);
            if (resource.exists()) {
                // 使用InputStream读取资源内容（支持jar包）
                try (InputStream inputStream = resource.getInputStream()) {
                    byte[] content = readAllBytes(inputStream);

                    // 创建临时文件
                    String fileName = relativePath.substring(relativePath.lastIndexOf('/') + 1);
                    String prefix = fileName.substring(0, fileName.lastIndexOf('.'));
                    String suffix = fileName.substring(fileName.lastIndexOf('.'));

                    Path tempFile = Files.createTempFile(prefix, suffix);
                    Files.write(tempFile, content, StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING);

                    String absolutePath = tempFile.toAbsolutePath().toString();

                    // 记录临时文件路径，用于清理
                    synchronized (tempFilePaths) {
                        tempFilePaths.add(absolutePath);
                    }

                    log.info("从classpath创建临时密钥文件: {} -> {}", relativePath, absolutePath);
                    return absolutePath;
                }
            }
        } catch (Exception e) {
            log.warn("从classpath创建临时密钥文件失败: {}", e.getMessage());
        }

        // 方案2：备用方案，使用项目根目录
        String projectRoot = System.getProperty("user.dir");
        Path keyFilePath = Paths.get(projectRoot, "src", "main", "resources", relativePath);

        if (Files.exists(keyFilePath)) {
            String absolutePath = keyFilePath.toAbsolutePath().toString();
            log.info("从项目目录找到密钥文件: {}", absolutePath);
            return absolutePath;
        }

        throw new IOException("密钥文件不存在: " + relativePath);
    }

    /**
     * 清理临时文件
     */
    @PreDestroy
    public void cleanup() {
        log.info("开始清理拉卡拉SDK临时文件...");

        synchronized (tempFilePaths) {
            for (String filePath : tempFilePaths) {
                try {
                    Path path = Paths.get(filePath);
                    if (Files.exists(path) && Files.deleteIfExists(path)) {
                        log.debug("删除临时文件: {}", filePath);
                    }
                } catch (Exception e) {
                    log.warn("删除临时文件失败: {}, 错误: {}", filePath, e.getMessage());
                }
            }
            tempFilePaths.clear();
        }

        log.info("拉卡拉SDK临时文件清理完成");
    }

    /**
     * 读取InputStream的所有字节（Java 8兼容版本）
     */
    private byte[] readAllBytes(InputStream inputStream) throws IOException {
        ByteArrayOutputStream buffer = new ByteArrayOutputStream();
        byte[] data = new byte[1024];
        int nRead;
        while ((nRead = inputStream.read(data, 0, data.length)) != -1) {
            buffer.write(data, 0, nRead);
        }
        return buffer.toByteArray();
    }

}
