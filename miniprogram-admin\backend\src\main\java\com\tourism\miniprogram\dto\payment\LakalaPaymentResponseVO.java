package com.tourism.miniprogram.dto.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 拉卡拉支付响应VO
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Data
@ApiModel(value = "拉卡拉支付响应", description = "拉卡拉支付预下单响应结果")
public class LakalaPaymentResponseVO {

    @ApiModelProperty(value = "响应码")
    private String code;

    @ApiModelProperty(value = "响应消息")
    private String msg;

    @ApiModelProperty(value = "商户号")
    private String merchantNo;

    @ApiModelProperty(value = "终端号")
    private String termNo;

    @ApiModelProperty(value = "商户订单号")
    private String outTradeNo;

    @ApiModelProperty(value = "拉卡拉订单号")
    private String lakalaOrderNo;

    @ApiModelProperty(value = "微信预支付交易会话标识")
    private String prepayId;

    @ApiModelProperty(value = "微信小程序支付参数")
    private WechatMiniProgramPayParams payParams;

    /**
     * 微信小程序支付参数
     */
    @Data
    @ApiModel(value = "微信小程序支付参数")
    public static class WechatMiniProgramPayParams {

        @ApiModelProperty(value = "小程序ID")
        private String appId;

        @ApiModelProperty(value = "时间戳")
        private String timeStamp;

        @ApiModelProperty(value = "随机字符串")
        private String nonceStr;

        @ApiModelProperty(value = "订单详情扩展字符串")
        private String packageValue;

        @ApiModelProperty(value = "签名方式")
        private String signType;

        @ApiModelProperty(value = "签名")
        private String paySign;
    }

    /**
     * 判断是否成功
     */
    public boolean isSuccess() {
        return "0000".equals(code);
    }

    /**
     * 构建微信小程序支付参数
     */
    public static WechatMiniProgramPayParams buildPayParams(String appId, String prepayId, 
                                                           String timeStamp, String nonceStr, String paySign) {
        WechatMiniProgramPayParams params = new WechatMiniProgramPayParams();
        params.setAppId(appId);
        params.setTimeStamp(timeStamp);
        params.setNonceStr(nonceStr);
        params.setPackageValue("prepay_id=" + prepayId);
        params.setSignType("RSA");
        params.setPaySign(paySign);
        return params;
    }
}
