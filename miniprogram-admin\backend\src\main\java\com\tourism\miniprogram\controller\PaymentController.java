package com.tourism.miniprogram.controller;

import com.tourism.miniprogram.common.Result;
import com.tourism.miniprogram.dto.payment.LakalaPaymentResponseVO;
import com.tourism.miniprogram.service.PaymentAdapterService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.constraints.NotNull;
import java.io.BufferedReader;
import java.io.IOException;
import java.math.BigDecimal;

/**
 * 支付控制器
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Slf4j
@RestController
@RequestMapping("/payment")
@Api(tags = "支付管理", description = "支付相关接口")
public class PaymentController {

    @Autowired
    private PaymentAdapterService paymentAdapterService;

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    @ApiOperation(value = "创建支付订单", notes = "创建拉卡拉支付订单，返回微信小程序支付参数")
    public Result<LakalaPaymentResponseVO> createPayment(
            @ApiParam(value = "订单ID", required = true) @RequestParam @NotNull Integer orderId,
            @ApiParam(value = "用户OpenId", required = true) @RequestParam @NotNull String userId,
            @ApiParam(value = "支付金额", required = true) @RequestParam @NotNull BigDecimal totalAmount,
            @ApiParam(value = "商品标题", required = true) @RequestParam @NotNull String subject,
            @ApiParam(value = "商品详情") @RequestParam(required = false) String detail,
            HttpServletRequest request) {
        
        try {
            log.info("创建支付订单请求，订单ID: {}, 用户ID: {}, 金额: {}", orderId, userId, totalAmount);

            // 获取客户端IP
            String clientIp = getClientIpAddress(request);

            // 创建支付订单
            LakalaPaymentResponseVO response = paymentAdapterService.createPayment(
                orderId, userId, totalAmount, subject, detail, clientIp);

            if (response.isSuccess()) {
                log.info("支付订单创建成功，订单ID: {}", orderId);
                return Result.success(response);
            } else {
                log.error("支付订单创建失败，订单ID: {}, 错误信息: {}", orderId, response.getMsg());
                return Result.error("创建支付订单失败: " + response.getMsg());
            }

        } catch (Exception e) {
            log.error("创建支付订单异常，订单ID: {}", orderId, e);
            return Result.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付状态
     */
    @GetMapping("/status/{orderId}")
    @ApiOperation(value = "查询支付状态", notes = "查询订单支付状态")
    public Result<LakalaPaymentResponseVO> queryPaymentStatus(
            @ApiParam(value = "订单ID", required = true) @PathVariable @NotNull Integer orderId) {
        
        try {
            log.info("查询支付状态，订单ID: {}", orderId);

            LakalaPaymentResponseVO response = paymentAdapterService.queryPaymentStatus(orderId);
            return Result.success(response);

        } catch (Exception e) {
            log.error("查询支付状态异常，订单ID: {}", orderId, e);
            return Result.error("查询支付状态失败: " + e.getMessage());
        }
    }

    /**
     * 申请退款
     */
    @PostMapping("/refund")
    @ApiOperation(value = "申请退款", notes = "申请订单退款")
    public Result<String> refundPayment(
            @ApiParam(value = "订单ID", required = true) @RequestParam @NotNull Integer orderId,
            @ApiParam(value = "退款金额", required = true) @RequestParam @NotNull BigDecimal refundAmount,
            @ApiParam(value = "退款原因") @RequestParam(required = false) String refundReason) {
        
        try {
            log.info("申请退款，订单ID: {}, 退款金额: {}", orderId, refundAmount);

            boolean result = paymentAdapterService.refundPayment(orderId, refundAmount, refundReason);
            
            if (result) {
                return Result.success("退款申请成功");
            } else {
                return Result.error("退款申请失败");
            }

        } catch (Exception e) {
            log.error("申请退款异常，订单ID: {}", orderId, e);
            return Result.error("申请退款失败: " + e.getMessage());
        }
    }

    /**
     * 接收拉卡拉支付结果通知
     */
    @PostMapping("/notify")
    @ApiOperation(value = "支付结果通知", notes = "接收拉卡拉支付结果异步通知")
    public String handlePaymentNotify(HttpServletRequest request) {
        try {
            log.info("接收拉卡拉支付回调通知");

            // 读取请求体数据
            String notifyData = readRequestBody(request);
            log.debug("支付回调数据: {}", notifyData);

            // 处理支付回调
            boolean result = paymentAdapterService.handlePaymentCallback(notifyData);

            if (result) {
                log.info("支付回调处理成功");
                return "SUCCESS"; // 返回SUCCESS表示处理成功
            } else {
                log.error("支付回调处理失败");
                return "FAIL"; // 返回FAIL表示处理失败
            }

        } catch (Exception e) {
            log.error("处理支付回调异常", e);
            return "FAIL";
        }
    }

    /**
     * 获取客户端IP地址
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty() && !"unknown".equalsIgnoreCase(xForwardedFor)) {
            return xForwardedFor.split(",")[0].trim();
        }

        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty() && !"unknown".equalsIgnoreCase(xRealIp)) {
            return xRealIp;
        }

        return request.getRemoteAddr();
    }

    /**
     * 读取请求体数据
     */
    private String readRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }
}
