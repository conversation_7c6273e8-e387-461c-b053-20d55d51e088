package com.tourism.miniprogram.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.tourism.miniprogram.config.LakalaPaymentConfig;
import com.tourism.miniprogram.dto.payment.LakalaPaymentRequestDTO;
import com.tourism.miniprogram.dto.payment.LakalaPaymentResponseVO;
import com.tourism.miniprogram.dto.payment.PaymentNotifyDTO;
import com.tourism.miniprogram.entity.Order;
import com.tourism.miniprogram.service.LakalaPaymentService;
import com.tourism.miniprogram.service.OrderService;
import com.tourism.miniprogram.service.PaymentAdapterService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * 支付适配器服务实现类
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Slf4j
@Service
public class PaymentAdapterServiceImpl implements PaymentAdapterService {

    @Autowired
    private LakalaPaymentService lakalaPaymentService;

    @Autowired
    private OrderService orderService;

    @Autowired
    private LakalaPaymentConfig paymentConfig;

    @Autowired
    private ObjectMapper objectMapper;

    @Value("${wechat.miniapp.app-id}")
    private String wechatAppId;

    @Override
    public LakalaPaymentResponseVO createPayment(Integer orderId, String userId, BigDecimal totalAmount, 
                                               String subject, String detail, String clientIp) {
        try {
            log.info("创建支付订单，订单ID: {}, 用户ID: {}, 金额: {}", orderId, userId, totalAmount);

            // 获取订单信息
            Order order = orderService.getById(orderId);
            if (order == null) {
                throw new RuntimeException("订单不存在");
            }

            // 验证订单状态
            if (!"pending".equals(order.getStatus())) {
                throw new RuntimeException("订单状态不正确，无法支付");
            }

            // 验证订单金额
            if (order.getTotalAmount().compareTo(totalAmount) != 0) {
                throw new RuntimeException("订单金额不匹配");
            }

            // 构建拉卡拉支付请求
            LakalaPaymentRequestDTO request = new LakalaPaymentRequestDTO();
            request.setMerchantNo(paymentConfig.getMerchantNo());
            request.setTermNo(paymentConfig.getTermNo());
            request.setOutTradeNo(order.getOrderNo());
            request.setTotalAmount(totalAmount);
            request.setTransType("71"); // 微信小程序
            request.setAccountType("WECHAT");
            request.setSubAppid(wechatAppId);
            request.setUserId(userId);
            request.setSubject(subject);
            request.setNotifyUrl(paymentConfig.getNotifyUrl());
            request.setRequestIp(clientIp);
            request.setTimeoutExpress(30); // 30分钟超时

            // 构建商品详情JSON
            request.buildDetailJson(subject, totalAmount, 1);

            // 调用拉卡拉支付服务
            LakalaPaymentResponseVO response = lakalaPaymentService.createPaymentOrder(request);

            log.info("支付订单创建完成，订单ID: {}, 响应码: {}", orderId, response.getCode());
            return response;

        } catch (Exception e) {
            log.error("创建支付订单失败，订单ID: {}", orderId, e);
            throw new RuntimeException("创建支付订单失败: " + e.getMessage(), e);
        }
    }

    @Override
    public LakalaPaymentResponseVO queryPaymentStatus(Integer orderId) {
        try {
            log.info("查询支付状态，订单ID: {}", orderId);

            // 获取订单信息
            Order order = orderService.getById(orderId);
            if (order == null) {
                throw new RuntimeException("订单不存在");
            }

            // 调用拉卡拉查询接口
            return lakalaPaymentService.queryOrderStatus(order.getOrderNo());

        } catch (Exception e) {
            log.error("查询支付状态失败，订单ID: {}", orderId, e);
            throw new RuntimeException("查询支付状态失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean refundPayment(Integer orderId, BigDecimal refundAmount, String refundReason) {
        try {
            log.info("申请退款，订单ID: {}, 退款金额: {}", orderId, refundAmount);

            // 获取订单信息
            Order order = orderService.getById(orderId);
            if (order == null) {
                throw new RuntimeException("订单不存在");
            }

            // 验证订单状态
            if (!"paid".equals(order.getStatus())) {
                throw new RuntimeException("订单状态不正确，无法退款");
            }

            // 验证退款金额
            if (refundAmount.compareTo(order.getTotalAmount()) > 0) {
                throw new RuntimeException("退款金额不能大于订单金额");
            }

            // 调用拉卡拉退款接口
            boolean refundResult = lakalaPaymentService.refundOrder(order.getOrderNo(), refundAmount, refundReason);

            if (refundResult) {
                // 更新订单状态为已退款
                orderService.refundOrder(orderId);
                log.info("退款成功，订单ID: {}", orderId);
            }

            return refundResult;

        } catch (Exception e) {
            log.error("申请退款失败，订单ID: {}", orderId, e);
            return false;
        }
    }

    @Override
    public boolean handlePaymentCallback(String notifyData) {
        try {
            log.info("处理支付回调，数据: {}", notifyData);

            // 解析回调数据
            PaymentNotifyDTO notifyDTO = parseNotifyData(notifyData);
            if (notifyDTO == null) {
                log.error("解析支付回调数据失败");
                return false;
            }

            // 调用拉卡拉支付服务处理回调
            return lakalaPaymentService.handlePaymentNotify(notifyDTO);

        } catch (Exception e) {
            log.error("处理支付回调失败", e);
            return false;
        }
    }

    /**
     * 解析回调通知数据
     */
    private PaymentNotifyDTO parseNotifyData(String notifyData) {
        try {
            // 这里根据拉卡拉的回调数据格式进行解析
            // 假设是JSON格式，实际可能是表单格式
            return objectMapper.readValue(notifyData, PaymentNotifyDTO.class);
        } catch (Exception e) {
            log.error("解析回调数据失败", e);
            return null;
        }
    }
}
