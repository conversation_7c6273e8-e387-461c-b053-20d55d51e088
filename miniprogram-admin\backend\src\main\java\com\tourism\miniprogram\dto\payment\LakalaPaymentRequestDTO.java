package com.tourism.miniprogram.dto.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.DecimalMin;
import java.math.BigDecimal;

/**
 * 拉卡拉支付请求DTO
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
@Data
@ApiModel(value = "拉卡拉支付请求", description = "拉卡拉支付预下单请求参数")
public class LakalaPaymentRequestDTO {

    @ApiModelProperty(value = "商户号", required = true)
    @NotBlank(message = "商户号不能为空")
    private String merchantNo;

    @ApiModelProperty(value = "终端号", required = true)
    @NotBlank(message = "终端号不能为空")
    private String termNo;

    @ApiModelProperty(value = "商户订单号", required = true)
    @NotBlank(message = "商户订单号不能为空")
    private String outTradeNo;

    @ApiModelProperty(value = "订单金额（元）", required = true)
    @NotNull(message = "订单金额不能为空")
    @DecimalMin(value = "0.01", message = "订单金额必须大于0")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "交易类型（71-微信小程序）", required = true)
    @NotBlank(message = "交易类型不能为空")
    private String transType = "71";

    @ApiModelProperty(value = "账户类型（WECHAT-微信）", required = true)
    @NotBlank(message = "账户类型不能为空")
    private String accountType = "WECHAT";

    @ApiModelProperty(value = "微信小程序AppId", required = true)
    @NotBlank(message = "微信小程序AppId不能为空")
    private String subAppid;

    @ApiModelProperty(value = "用户OpenId", required = true)
    @NotBlank(message = "用户OpenId不能为空")
    private String userId;

    @ApiModelProperty(value = "商品标题", required = true)
    @NotBlank(message = "商品标题不能为空")
    private String subject;

    @ApiModelProperty(value = "商品详情（JSON格式）")
    private String detail;

    @ApiModelProperty(value = "异步通知地址", required = true)
    @NotBlank(message = "异步通知地址不能为空")
    private String notifyUrl;

    @ApiModelProperty(value = "客户端IP", required = true)
    @NotBlank(message = "客户端IP不能为空")
    private String requestIp;

    @ApiModelProperty(value = "位置信息")
    private String location;

    @ApiModelProperty(value = "订单超时时间（分钟）")
    private Integer timeoutExpress = 30;

    @ApiModelProperty(value = "签名")
    private String sign;

    /**
     * 构建商品详情JSON
     */
    public void buildDetailJson(String productName, BigDecimal price, Integer quantity) {
        this.detail = String.format(
            "{\"goods_detail\":[{\"goods_id\":\"%s\",\"goods_name\":\"%s\",\"price\":%s,\"quantity\":%d}]}",
            outTradeNo, productName, price, quantity
        );
    }
}
