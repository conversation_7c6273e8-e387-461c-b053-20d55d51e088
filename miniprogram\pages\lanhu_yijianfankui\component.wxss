.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}
.box_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.section_1 {
  width: 402rpx;
  height: 46rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 48rpx 0 22rpx 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 12rpx;
}
.text_1 {
  width: 128rpx;
  height: 44rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 32rpx;
}
.box_2 {
  width: 750rpx;
  height: 1450rpx;
  margin-bottom: 2rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_1 {
  height: 594rpx;
  background: url(data:image/png;base64,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) 100% no-repeat;
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  width: 594rpx;
  margin: 78rpx 0 0 78rpx;
}
.text_2 {
  width: 266rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 554rpx 0 0 164rpx;
}
.group_1 {
  background-color: rgba(64,128,255,1.000000);
  border-radius: 200rpx;
  width: 690rpx;
  height: 100rpx;
  display: flex;
  flex-direction: row;
  margin: 542rpx 0 0 30rpx;
}
.image-text_1 {
  width: 186rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 34rpx 0 0 252rpx;
}
.thumbnail_4 {
  width: 32rpx;
  height: 32rpx;
  margin-top: 2rpx;
}
.text-group_1 {
  width: 128rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(255,255,255,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.group_2 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 102rpx 0 26rpx 232rpx;
}

/* 反馈列表样式 */
.feedback-list {
  padding: 20rpx;
}

.feedback-item {
  background-color: #fff;
  border-radius: 16rpx;
  margin-bottom: 20rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.feedback-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.feedback-status {
  font-size: 26rpx;
  color: #007aff;
  font-weight: bold;
}

.status-pending {
  color: #909399;
}

.status-processing {
  color: #E6A23C;
}

.status-resolved {
  color: #67C23A;
}

.status-closed {
  color: #F56C6C;
}

.feedback-time {
  font-size: 24rpx;
  color: #999;
}

.feedback-content {
  margin-bottom: 20rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.6;
  font-weight: 400;
}

.feedback-reply {
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #007aff;
  margin-top: 20rpx;
}

.reply-label {
  font-size: 24rpx;
  color: #007aff;
  font-weight: 500;
  margin-right: 8rpx;
}

.reply-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  font-weight: 400;
}

/* 浮动添加按钮 */
.floating-add-btn {
  position: fixed;
  bottom: 120rpx;
  right: 40rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #007aff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
  z-index: 100;
  transition: all 0.3s ease;
}

.floating-add-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.2);
}

.add-btn-text {
  color: white;
  font-size: 48rpx;
  font-weight: 300;
  line-height: 1;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.6);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-content {
  width: 90%;
  max-width: 600rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 20rpx 60rpx rgba(102, 126, 234, 0.3);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(100rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 40rpx 30rpx 20rpx;
  background: white;
  color: black;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: black;
}

.modal-close {
  font-size: 44rpx;
  color: rgba(0, 0, 0, 0.8);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:active {
  background: rgba(255,255,255,0.2);
  color: white;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.form-item {
  margin-bottom: 36rpx;
}

.form-label {
  display: block;
  font-size: 30rpx;
  color: #2c3e50;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.form-input {
  width: 100%;
  height: 88rpx;
  border: 2rpx solid #e1e8ed;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background: rgba(255,255,255,0.8);
  transition: all 0.3s ease;
}

.form-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.form-textarea {
  width: 100%;
  min-height: 180rpx;
  border: 2rpx solid #e1e8ed;
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 30rpx;
  box-sizing: border-box;
  background: rgba(255,255,255,0.8);
  transition: all 0.3s ease;
  line-height: 1.6;
}

.form-textarea:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}

.modal-footer {
  display: flex;
  padding: 20rpx 30rpx 40rpx;
  gap: 24rpx;
}

.btn-cancel {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: #5a6c7d;
  border: none;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.2s ease;
}

.btn-cancel:active {
  transform: scale(0.98);
  background: linear-gradient(135deg, #e8eaf6 0%, #b8c6db 100%);
}

.btn-submit {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 30rpx;
  font-weight: 500;
  transition: all 0.2s ease;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
}

.btn-submit:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(102, 126, 234, 0.2);
}

.btn-submit[disabled] {
  background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
  box-shadow: none;
  transform: none;
}