package com.tourism.miniprogram.service;

import com.tourism.miniprogram.dto.payment.LakalaPaymentRequestDTO;
import com.tourism.miniprogram.dto.payment.LakalaPaymentResponseVO;
import com.tourism.miniprogram.dto.payment.PaymentNotifyDTO;

/**
 * 拉卡拉支付服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
public interface LakalaPaymentService {

    /**
     * 创建支付订单（预下单）
     *
     * @param request 支付请求参数
     * @return 支付响应结果
     */
    LakalaPaymentResponseVO createPaymentOrder(LakalaPaymentRequestDTO request);

    /**
     * 查询订单状态
     *
     * @param outTradeNo 商户订单号
     * @return 订单状态信息
     */
    LakalaPaymentResponseVO queryOrderStatus(String outTradeNo);

    /**
     * 处理支付回调通知
     *
     * @param notifyData 回调通知数据
     * @return 处理结果
     */
    boolean handlePaymentNotify(PaymentNotifyDTO notifyData);

    /**
     * 验证回调签名
     *
     * @param notifyData 回调通知数据
     * @return 验证结果
     */
    boolean verifyNotifySignature(PaymentNotifyDTO notifyData);

    /**
     * 申请退款
     *
     * @param outTradeNo 商户订单号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    boolean refundOrder(String outTradeNo, java.math.BigDecimal refundAmount, String refundReason);
}
