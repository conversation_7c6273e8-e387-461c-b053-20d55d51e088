{"description": "Project configuration file", "packOptions": {"ignore": [], "include": []}, "setting": {"urlCheck": true, "es6": true, "enhance": false, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "bundle": false, "useIsolateContext": true, "useCompilerModule": true, "userConfirmedUseCompilerModuleSwitch": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "compileWorklet": false, "minifyWXML": true, "localPlugins": false, "disableUseStrict": false, "useCompilerPlugins": false, "condition": false, "swc": false, "disableSWC": true}, "compileType": "miniprogram", "libVersion": "3.7.12", "appid": "wxc793cea808daba50", "projectname": "miniprogram", "isGameTourist": false, "condition": {"search": {"list": []}, "conversation": {"list": []}, "game": {"list": []}, "plugin": {"list": []}, "gamePlugin": {"list": []}, "miniprogram": {"list": []}}, "simulatorPluginLibVersion": {}, "editorSetting": {}}