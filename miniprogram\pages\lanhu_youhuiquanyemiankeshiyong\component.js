const apiService = require('../../utils/apiService');
const userService = require('../../utils/userService');
const couponService = require('../../utils/couponService');

Page({
  data: {
    userId: null,
    coupons: [],
    loading: false,
    activeTab: 'all', // all, unused, used
    tabs: [{
        key: 'all',
        name: '全部',
        status: null,
        count: 0
      },
      {
        key: 'unused',
        name: '未使用',
        status: 'unactivated',
        count: 0
      },
      {
        key: 'used',
        name: '已使用',
        status: 'used',
        count: 0
      }
    ],
    allCoupons: [], // 存储所有门票数据用于筛选
    showUsageModal: false,
    usageModalData: [],
    shareCoupon: null // 当前要分享的卡券
  },
  // 页面生命周期
  onLoad: function (options) {
    console.info("门票列表页面加载");
    // 接收页面参数
    if (options.status) {
      this.setData({
        activeTab: options.status
      });
    }
    if (options.userId) {
      this.setData({
        userId: parseInt(options.userId)
      });
    }
    this.initPage();
  },

  onShow: function () {
    // 页面显示时刷新数据
    if (this.data.userId) {
      this.loadCoupons(true);
    }
  },

  onUnload: function () {
    console.info("门票列表页面卸载");
  },

  // 用户点击右上角分享
  onShareAppMessage: function () {
    const shareCoupon = this.data.shareCoupon;
    if (shareCoupon) {
      return {
        title: `${shareCoupon.scenicName} - 数字门票`,
        path: `/pages/lanhu_lingqukaquan/component?code=${shareCoupon.code}`,
        imageUrl: shareCoupon.scenicImage || ''
      };
    }

    // 默认分享内容
    return {
      title: '我的卡券 - 旅游讲解小程序',
      path: '/pages/lanhu_youhuiquanyemiankeshiyong/component',
      imageUrl: ''
    };
  },

  // 下拉刷新
  onPullDownRefresh: function () {
    this.loadCoupons(true).finally(() => {
      wx.stopPullDownRefresh();
    });
  },

  // 移除上拉加载更多逻辑，因为直接加载全部数据
  // 初始化页面
  async initPage() {
    try {
      // 获取当前用户信息
      let userInfo;
      try {
        // 先尝试从本地存储获取用户信息
        const storedUserInfo = wx.getStorageSync('userInfo');
        if (storedUserInfo && storedUserInfo.id) {
          userInfo = storedUserInfo;
        } else {
          // 如果没有本地用户信息，使用默认用户ID
          userInfo = {
            id: 1,
            name: '用户'
          };
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
        // 使用默认用户ID
        userInfo = {
          id: 1,
          name: '用户'
        };
      }

      if (!userInfo || !userInfo.id) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }

      this.setData({
        userId: userInfo.id
      });

      // 加载门票列表
      await this.loadCoupons(true);
    } catch (error) {
      console.error('初始化页面失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }
  },

  // 加载门票列表 - 直接加载全部数据
  async loadCoupons(reset = false) {
    if (this.data.loading) return;

    try {
      this.setData({
        loading: true
      });

      // 显示加载提示
      wx.showLoading({
        title: '加载中...'
      });

      if (reset) {
        this.setData({
          coupons: []
        });
      }

      // 获取用户的所有卡券，使用大的分页参数来获取全部数据
      console.log('开始获取用户卡券，用户ID:', this.data.userId);
      const response = await apiService.getUserCoupons(this.data.userId, {
        current: 1,
        size: 1000 // 使用大的size来获取所有数据
      });

      console.log('卡券API响应:', response);
      if (response && response.records && response.records.length > 0) {
        // 过滤掉expired状态的卡券
        const validCoupons = response.records.filter(coupon => coupon.status !== 'expired');
        console.log(`过滤前卡券数量: ${response.records.length}, 过滤后数量: ${validCoupons.length}`);

        // 为每张卡券获取产品和景区详情
        const enrichedCoupons = await this.enrichCouponsWithDetails(validCoupons);

        // 存储所有门票数据
        this.setData({
          allCoupons: enrichedCoupons
        });

        // 更新标签计数
        this.updateTabCounts(enrichedCoupons);

        // 根据当前选中的tab过滤门票
        this.filterCoupons();
      } else {
        console.log('没有获取到卡券数据或数据为空');
        this.setData({
          allCoupons: [],
          coupons: []
        });
        this.updateTabCounts([]);
      }
    } catch (error) {
      console.error('加载门票列表失败:', error);
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      });
    } finally {
      // 隐藏加载提示
      wx.hideLoading();
      this.setData({
        loading: false
      });
    }
  },

  // 为卡券获取产品和景区详情
  async enrichCouponsWithDetails(coupons) {
    const enrichedCoupons = [];

    for (const coupon of coupons) {
      try {
        const enrichedCoupon = {
          ...coupon
        };

        // 根据productId获取产品详情
        if (coupon.productId) {
          try {
            const productDetail = await apiService.getProductDetail(coupon.productId);
            enrichedCoupon.productDetail = productDetail;
            console.log(`获取产品详情成功 (产品ID: ${coupon.productId}):`, productDetail);

            // 根据产品的景区ID获取景区详情
            if (productDetail && productDetail.scenicId) {
              try {
                let scenicIdToQuery = productDetail.scenicId;
                let isBundle = false;

                // 检查是否为组合包产品
                if (productDetail.productType === 'bundle') {
                  isBundle = true;
                  // 如果是组合包，scenic_id 包含多个景区ID（用逗号分隔），只取第一个用于显示
                  const scenicIds = productDetail.scenicId.split(',');
                  scenicIdToQuery = scenicIds[0].trim();
                  console.log(`组合包产品，所有景区ID: ${productDetail.scenicId}，使用第一个景区ID: ${scenicIdToQuery}`);

                  // 保存所有景区ID用于使用情况查询
                  enrichedCoupon.allScenicIds = scenicIds.map(id => id.trim());
                }

                const scenicDetail = await apiService.getScenicDetail(scenicIdToQuery);

                // 获取省份和城市名称
                if (scenicDetail && scenicDetail.provinceId && scenicDetail.cityId) {
                  try {
                    // 并行获取省份和城市信息以提高性能
                    const [provinceDetail, cityDetail] = await Promise.all([
                      apiService.getProvinceById(scenicDetail.provinceId).catch(err => {
                        console.error(`获取省份信息失败 (省份ID: ${scenicDetail.provinceId}):`, err);
                        return null;
                      }),
                      apiService.getCityById(scenicDetail.cityId).catch(err => {
                        console.error(`获取城市信息失败 (城市ID: ${scenicDetail.cityId}):`, err);
                        return null;
                      })
                    ]);

                    scenicDetail.provinceName = provinceDetail ? provinceDetail.name : '';
                    scenicDetail.cityName = cityDetail ? cityDetail.name : '';
                  } catch (locationError) {
                    console.error('获取省份城市信息失败:', locationError);
                    scenicDetail.provinceName = '';
                    scenicDetail.cityName = '';
                  }
                }

                enrichedCoupon.scenicDetail = scenicDetail;
                enrichedCoupon.isBundle = isBundle;
                console.log(`获取景区详情成功 (景区ID: ${scenicIdToQuery}):`, scenicDetail);
              } catch (scenicError) {
                console.error(`获取景区详情失败 (景区ID: ${productDetail.scenicId}):`, scenicError);
                enrichedCoupon.scenicDetail = null;
              }
            }
          } catch (productError) {
            console.error(`获取产品详情失败 (产品ID: ${coupon.productId}):`, productError);
            enrichedCoupon.productDetail = null;
            enrichedCoupon.scenicDetail = null;
          }
        }

        enrichedCoupons.push(enrichedCoupon);
      } catch (error) {
        console.error('处理卡券详情失败:', error);
        // 即使获取详情失败，也保留原始卡券数据
        enrichedCoupons.push({
          ...coupon
        });
      }
    }

    return enrichedCoupons;
  },

  // 更新标签计数
  updateTabCounts(allCoupons) {
    const tabs = this.data.tabs.map(tab => {
      if (tab.key === 'all') {
        // 全部标签只计算非expired状态的卡券
        tab.count = allCoupons.filter(coupon => coupon.status !== 'expired').length;
      } else if (tab.key === 'unused') {
        tab.count = allCoupons.filter(coupon =>
          coupon.status === 'unactivated' || coupon.status === 'active'
        ).length;
      } else if (tab.key === 'used') {
        // 已使用标签只计算used状态的卡券，不包含expired
        tab.count = allCoupons.filter(coupon =>
          coupon.status === 'used'
        ).length;
      }
      return tab;
    });

    this.setData({
      tabs
    });
  },

  // 根据当前tab过滤门票
  filterCoupons() {
    const {
      allCoupons,
      activeTab
    } = this.data;

    // 首先过滤掉所有expired状态的卡券
    let filteredCoupons = allCoupons.filter(coupon => coupon.status !== 'expired');

    if (activeTab === 'unused') {
      filteredCoupons = filteredCoupons.filter(coupon =>
        coupon.status === 'unactivated' || coupon.status === 'active'
      );
    } else if (activeTab === 'used') {
      // 已使用标签只显示used状态的卡券，不包含expired
      filteredCoupons = filteredCoupons.filter(coupon =>
        coupon.status === 'used'
      );
    }

    // 按创建时间倒序排列并预处理数据
    filteredCoupons.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    // 预处理数据，添加格式化的字段
    const processedCoupons = filteredCoupons.map(coupon => {
      const processed = {
        ...coupon,
        formattedCreatedAt: this.formatDate(coupon.createdAt),
        formattedValidFrom: this.formatDate(coupon.validFrom),
        formattedValidTo: this.formatDate(coupon.validTo),
        formattedUsedAt: this.formatDate(coupon.usedAt),
        statusText: this.getStatusText(coupon.status),
        statusClass: this.getStatusClass(coupon.status),
        // 添加产品和景区信息的显示字段
        productName: coupon.productDetail ? coupon.productDetail.name : '未知产品',
        productPrice: coupon.productDetail ? coupon.productDetail.price : 0,
        scenicName: coupon.scenicDetail ?
          (coupon.isBundle ? `${coupon.scenicDetail.title}等（组合包）` : coupon.scenicDetail.title) :
          '未知景区',
        scenicImage: coupon.scenicDetail ? coupon.scenicDetail.image : '',
        scenicLocation: coupon.scenicDetail ? `${coupon.scenicDetail.provinceName || ''}${coupon.scenicDetail.cityName || ''}` : '',
        scenicAddress: coupon.scenicDetail ? coupon.scenicDetail.address : '',
        isBundle: coupon.isBundle || false,
        allScenicIds: coupon.allScenicIds || []
      };

      // 添加调试日志
      console.log(`卡券 ${coupon.id} 处理结果:`, {
        productDetail: coupon.productDetail,
        scenicDetail: coupon.scenicDetail,
        productName: processed.productName,
        scenicName: processed.scenicName,
        scenicLocation: processed.scenicLocation
      });

      return processed;
    });

    this.setData({
      coupons: processedCoupons
    });
  },

  // 切换标签
  onTabChange(e) {
    const tabKey = e.currentTarget.dataset.tab;
    if (tabKey === this.data.activeTab) return;

    this.setData({
      activeTab: tabKey
    });

    this.filterCoupons();
  },

  // 格式化日期
  formatDate(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);

    // 格式化日期部分（年-月-日）
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    // 格式化时间部分（时:分:秒）
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    // 返回格式化的日期和时间
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 格式化日期时间（用于弹窗显示）
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 获取门票状态文本
  getStatusText(status) {
    const statusMap = {
      'unactivated': '未激活',
      'active': '已激活',
      'used': '已使用',
      'expired': '已过期'
    };
    return statusMap[status] || status;
  },

  // 获取门票状态样式类
  getStatusClass(status) {
    const classMap = {
      'unactivated': 'status-unactivated',
      'active': 'status-active',
      'used': 'status-used',
      'expired': 'status-expired'
    };
    return classMap[status] || '';
  },

  // 查看使用规则
  viewRules() {
    wx.showModal({
      title: '卡券使用规则',
      content: '1. 门票激活后12小时内有效\n2. 每张门票仅限使用一次',
      showCancel: false,
      confirmText: '我知道了'
    });
  },

  // 查看使用情况（组合包专用）
  async viewUsageStatus(e) {
    const couponId = e.currentTarget.dataset.couponId;
    const coupon = this.data.coupons.find(c => c.id === parseInt(couponId));

    if (!coupon || !coupon.isBundle) {
      wx.showToast({
        title: '仅组合包支持查看使用情况',
        icon: 'none'
      });
      return;
    }

    try {
      wx.showLoading({
        title: '加载中...'
      });

      // 调用API获取使用记录
      const usageRecords = await couponService.getCouponUsageRecordsByCode(coupon.code);

      // 获取组合包中所有景区的详细信息
      const scenicDetails = await this.getScenicDetailsForBundle(coupon.allScenicIds);

      wx.hideLoading();

      if (!usageRecords || usageRecords.length === 0) {
        // 即使没有使用记录，也显示景区信息
        this.showBundleUsageModal(scenicDetails, []);
        return;
      }

      // 显示包含景区详细信息的弹窗
      this.showBundleUsageModal(scenicDetails, usageRecords);

    } catch (error) {
      wx.hideLoading();
      console.error('获取使用情况失败:', error);
      wx.showToast({
        title: '获取使用情况失败',
        icon: 'none'
      });
    }
  },

  // 分享卡券
  shareCoupon(e) {
    const couponId = e.currentTarget.dataset.couponId;
    const coupon = this.data.coupons.find(c => c.id === parseInt(couponId));

    if (!coupon) {
      wx.showToast({
        title: '卡券不存在',
        icon: 'none'
      });
      return;
    }

    // 设置当前要分享的卡券信息
    this.setData({
      shareCoupon: coupon
    });

    console.log('准备分享卡券:', coupon);
  },

  // 获取组合包中所有景区的详细信息
  async getScenicDetailsForBundle(scenicIds) {
    const scenicDetails = [];

    for (const scenicId of scenicIds) {
      try {
        const scenicDetail = await apiService.getScenicDetail(scenicId);
        scenicDetails.push({
          scenicId: scenicId,
          title: scenicDetail ? scenicDetail.title : `景区${scenicId}`,
          address: scenicDetail ? scenicDetail.address : '',
          description: scenicDetail ? scenicDetail.description : ''
        });
      } catch (error) {
        console.error(`获取景区详情失败，景区ID: ${scenicId}`, error);
        scenicDetails.push({
          scenicId: scenicId,
          title: `景区${scenicId}`,
          address: '',
          description: ''
        });
      }
    }

    return scenicDetails;
  },

  // 显示组合包使用情况弹窗
  showBundleUsageModal(scenicDetails, usageRecords) {
    const modalData = scenicDetails.map(scenic => {
      // 查找对应的使用记录
      const record = usageRecords.find(r => r.scenicId === scenic.scenicId);

      let statusText = '未创建记录';
      let statusClass = 'unactivated';
      let formattedUsedAt = '';
      let formattedValidTo = '';

      if (record) {
        statusText = this.getStatusText(record.usageStatus);
        statusClass = record.usageStatus;

        if (record.usedAt) {
          formattedUsedAt = this.formatDateTime(record.usedAt);
        }
        if (record.validTo) {
          formattedValidTo = this.formatDateTime(record.validTo);
        }
      }

      return {
        scenicId: scenic.scenicId,
        title: scenic.title,
        address: scenic.address,
        description: scenic.description,
        statusText: statusText,
        statusClass: statusClass,
        usedAt: record ? record.usedAt : null,
        validTo: record ? record.validTo : null,
        formattedUsedAt: formattedUsedAt,
        formattedValidTo: formattedValidTo
      };
    });

    this.setData({
      showUsageModal: true,
      usageModalData: modalData
    });
  },

  // 隐藏使用情况弹窗
  hideUsageModal() {
    this.setData({
      showUsageModal: false,
      usageModalData: []
    });
  },



  // 格式化日期时间
  formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '';
    const date = new Date(dateTimeStr);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}`;
  },

  // 跳转到景区详情页面
  goToScenicDetail(e) {
    const couponId = e.currentTarget.dataset.couponId;
    const coupon = this.data.coupons.find(c => c.id === parseInt(couponId));

    if (coupon && coupon.scenicDetail && coupon.scenicDetail.scenicId) {
      wx.navigateTo({
        url: `/pages/lanhu_dulijingqu/component?scenicId=${coupon.scenicDetail.scenicId}`
      });
    } else if (coupon && coupon.productDetail && coupon.productDetail.scenicId) {
      // 如果景区详情获取失败，使用产品中的景区ID
      // 对于组合包，使用第一个景区ID
      let scenicId = coupon.productDetail.scenicId;
      if (coupon.isBundle && coupon.allScenicIds && coupon.allScenicIds.length > 0) {
        scenicId = coupon.allScenicIds[0];
      }
      wx.navigateTo({
        url: `/pages/lanhu_dulijingqu/component?scenicId=${scenicId}`
      });
    } else {
      wx.showToast({
        title: '景区信息不完整',
        icon: 'none'
      });
    }
  }
});