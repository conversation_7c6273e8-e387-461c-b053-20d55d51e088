package com.tourism.miniprogram.service;

import com.tourism.miniprogram.dto.payment.LakalaPaymentResponseVO;

import java.math.BigDecimal;

/**
 * 支付适配器服务接口
 * 统一支付接口，支持多种支付方式扩展
 *
 * <AUTHOR> Team
 * @since 2025-01-22
 */
public interface PaymentAdapterService {

    /**
     * 创建支付订单
     *
     * @param orderId 订单ID
     * @param userId 用户ID（OpenId）
     * @param totalAmount 支付金额
     * @param subject 商品标题
     * @param detail 商品详情
     * @param clientIp 客户端IP
     * @return 支付响应结果
     */
    LakalaPaymentResponseVO createPayment(Integer orderId, String userId, BigDecimal totalAmount,
                                         String subject, String detail, String clientIp);

    /**
     * 查询支付状态
     *
     * @param orderId 订单ID
     * @return 支付状态信息
     */
    LakalaPaymentResponseVO queryPaymentStatus(Integer orderId);

    /**
     * 申请退款
     *
     * @param orderId 订单ID
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 退款结果
     */
    boolean refundPayment(Integer orderId, BigDecimal refundAmount, String refundReason);

    /**
     * 处理支付回调
     *
     * @param notifyData 回调数据
     * @return 处理结果
     */
    boolean handlePaymentCallback(String notifyData);
}
