<template>
  <div class="guide-product-form">
    <div class="page-header">
      <h2>{{ isEdit ? '编辑讲解产品' : '新增讲解产品' }}</h2>
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-card>
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        style="max-width: 600px"
      >
        <el-form-item label="产品标题" prop="title">
          <el-input
            v-model="form.title"
            placeholder="请输入产品标题"
            maxlength="100"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="景区ID" prop="scenicId">
          <el-cascader
            v-model="form.scenicId"
            :options="provinceList"
            :props="{
              value: 'id',
              label: 'name',
              children: 'children',
              lazy: true,
              lazyLoad: loadCascaderData,
              emitPath: false,
              checkStrictly: false,
              leaf: 'isLeaf'
            }"
            placeholder="请选择景区"
            style="width: 100%"
            @change="handleScenicChange"
          >
            <template #default="{ node, data }">
              <span>{{ data.name }}</span>
            </template>
          </el-cascader>
        </el-form-item>

        <el-form-item label="讲解点数量" prop="pointCount">
          <el-input-number
            v-model="form.pointCount"
            :min="1"
            :max="999"
            placeholder="请输入讲解点数量"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="讲解时长" prop="duration">
          <el-input
            v-model="form.duration"
            type="textarea"
            :rows="3"
            placeholder="请输入讲解时长（2-3小时）"
            maxlength="500"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="产品价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入产品价格"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="背景图片" prop="backgroundImageUrl">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.backgroundImageUrl" :src="form.backgroundImageUrl" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="背景颜色" prop="backgroundColor">
          <div class="color-picker-container">
            <el-color-picker
              v-model="form.backgroundColor"
              show-alpha
              :predefine="predefineColors"
              @change="handleColorChange"
            />
            <el-input
              v-model="form.backgroundColor"
              placeholder="请选择背景颜色或输入颜色值"
              style="margin-left: 10px; width: 200px"
              maxlength="20"
            />
          </div>
          <div class="upload-tip">选择页面背景颜色，支持RGB格式（如 #FF5733）</div>
        </el-form-item>

        <el-form-item label="示例视频" prop="exampleVideoUrl">
          <el-upload
            class="video-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeVideoUpload"
            :http-request="handleVideoUpload"
            :disabled="uploadLoading"
          >
            <video v-if="form.exampleVideoUrl" :src="form.exampleVideoUrl" class="video-preview" controls />
            <div v-else-if="uploadLoading" class="uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>视频上传中...</div>
            </div>
            <div v-else class="uploader-placeholder">
              <el-icon class="uploader-icon"><Plus /></el-icon>
              <div>点击上传视频</div>
            </div>
          </el-upload>
          <div class="upload-tip">只能上传mp4/avi/mov格式视频，且不超过50MB</div>
        </el-form-item>

        <el-form-item label="讲解员" prop="lecturerId">
          <el-select
            v-model="form.lecturerId"
            placeholder="请选择讲解员"
            style="width: 100%"
            clearable
          >
            <el-option
              v-for="lecturer in lecturerList"
              :key="lecturer.id"
              :label="lecturer.name"
              :value="lecturer.id"
            >
              <span>{{ lecturer.name }} - {{ lecturer.title }}</span>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="地图图片" prop="mapUrl">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleMapUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.mapUrl" :src="form.mapUrl" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="收听图片" prop="startListeningImageUrl">
          <el-upload
            class="image-uploader"
            action="#"
            :show-file-list="false"
            :before-upload="beforeImageUpload"
            :http-request="handleListeningImageUpload"
            :disabled="uploadLoading"
          >
            <img v-if="form.startListeningImageUrl" :src="form.startListeningImageUrl" class="image" />
            <div v-else-if="uploadLoading" class="image-uploader-loading">
              <el-icon class="is-loading"><Loading /></el-icon>
              <div>上传中...</div>
            </div>
            <el-icon v-else class="image-uploader-icon"><Plus /></el-icon>
          </el-upload>
          <div class="upload-tip">只能上传jpg/png/gif/webp文件，且不超过2MB</div>
        </el-form-item>

        <el-form-item label="产品描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="4"
            placeholder="请输入产品描述"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="999"
            placeholder="请输入排序"
            style="width: 100%"
          />
        </el-form-item>

        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSubmit" :loading="loading">
            {{ isEdit ? '更新' : '创建' }}
          </el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button @click="handleBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Plus, Loading } from '@element-plus/icons-vue'
import { getGuideProductById, createGuideProduct, updateGuideProduct } from '@/api/guideProduct'
import { uploadImage, uploadVideo } from '@/api/upload'
import { getProvinceList } from '@/api/province'
import { getCityList } from '@/api/city'
import { getScenicPage } from '@/api/scenic'
import { getLecturerList } from '@/api/lecturer'

const router = useRouter()
const route = useRoute()

// 计算属性
const isEdit = computed(() => !!route.params.id)

// 响应式数据
const loading = ref(false)
const uploadLoading = ref(false)
const formRef = ref()
const provinceList = ref([])
const cityList = ref([])
const scenicList = ref([])
const lecturerList = ref([])
const cascaderLoading = ref(false)

const form = reactive({
  title: '',
  scenicId: null,
  pointCount: null,
  duration: '',
  price: null,
  backgroundImageUrl: '',
  backgroundColor: '',
  exampleVideoUrl: '',
  lecturerId: null,
  mapUrl: '',
  startListeningImageUrl: '',
  description: '',
  sort: 0,
  status: 1
})

// 表单验证规则
const rules = {
  title: [
    { required: true, message: '请输入产品标题', trigger: 'blur' },
    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
  ],
  scenicId: [
    { required: true, message: '请选择景区', trigger: 'blur' }
  ],
  pointCount: [
    { required: true, message: '请输入讲解点数量', trigger: 'blur' },
    { type: 'number', min: 1, max: 999, message: '讲解点数量必须在 1 到 999 之间', trigger: 'blur' }
  ],
  duration: [
    { required: true, message: '请输入讲解时长', trigger: 'blur' },
    { min: 1, max: 500, message: '长度在 1 到 500 个字符', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入产品价格', trigger: 'blur' },
    { type: 'number', min: 0, message: '价格不能小于0', trigger: 'blur' }
  ],
  sort: [
    { required: true, message: '请输入排序', trigger: 'blur' },
    { type: 'number', min: 0, max: 999, message: '排序必须在 0 到 999 之间', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}

// 获取省份列表
const fetchProvinceList = async () => {
  try {
    const { data } = await getProvinceList()
    provinceList.value = (data || []).map(province => ({
      ...province,
      children: []
    }))
  } catch (error) {
    console.error('获取省份列表失败:', error)
  }
}

// 获取讲师列表
const fetchLecturerList = async () => {
  try {
    const { data } = await getLecturerList()
    lecturerList.value = data || []
  } catch (error) {
    console.error('获取讲师列表失败:', error)
  }
}

// 获取城市列表
const fetchCityList = async (provinceId) => {
  if (!provinceId) {
    cityList.value = []
    return
  }
  
  try {
    const { data } = await getCityList(provinceId)
    const province = provinceList.value.find(p => p.id === provinceId)
    if (province) {
      province.children = (data || []).map(city => ({
        ...city,
        children: []
      }))
    }
  } catch (error) {
    console.error('获取城市列表失败:', error)
  }
}

// 获取景区列表
const fetchScenicList = async (cityId) => {
  if (!cityId) {
    scenicList.value = []
    return
  }
  
  try {
    const { data } = await getScenicPage({
      cityId,
      current: 1,
      size: 1000
    })
    const city = provinceList.value
      .flatMap(p => p.children)
      .find(c => c.id === cityId)
    if (city) {
      city.children = (data.records || []).map(scenic => ({
        id: scenic.scenicId,
        name: scenic.title,
        isLeaf: true,
        disabled: false
      }))
    }
  } catch (error) {
    console.error('获取景区列表失败:', error)
  }
}

// 级联选择器加载子节点数据
const loadCascaderData = async (node, resolve) => {
  try {
    if (node.level === 0) {
      // 加载省份数据
      if (provinceList.value.length === 0) {
        await fetchProvinceList()
      }
      resolve(provinceList.value)
    } else if (node.level === 1) {
      // 加载城市数据
      const provinceId = node.data.id
      await fetchCityList(provinceId)
      resolve(node.data.children)
    } else if (node.level === 2) {
      // 加载景区数据
      const cityId = node.data.id
      await fetchScenicList(cityId)
      resolve(node.data.children)
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    resolve([])
  }
}

// 省份变化处理
const handleProvinceChange = (provinceId) => {
  form.cityId = ''
  form.scenicId = null
  fetchCityList(provinceId)
}

// 城市变化处理
const handleCityChange = (cityId) => {
  form.scenicId = null
  fetchScenicList(cityId)
}

// 景区变化处理
const handleScenicChange = (value) => {
  console.log('选择的景区ID:', value)
  form.scenicId = value
}

// 预定义颜色
const predefineColors = ref([
  '#ff4500',
  '#ff8c00',
  '#ffd700',
  '#90ee90',
  '#00ced1',
  '#1e90ff',
  '#c71585',
  '#ff1493',
  '#00bfff',
  '#00ff00',
  '#ffff00',
  '#ff00ff',
  '#1e90ff',
  '#f0e68c',
  '#dda0dd',
  '#87ceeb'
])

// 颜色变化处理
const handleColorChange = (value) => {
  console.log('选择的颜色:', value)
  form.backgroundColor = value
}

// 图片上传前验证
const beforeImageUpload = (file) => {
  const isValidType = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'].includes(file.type)
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isValidType) {
    ElMessage.error('上传图片只能是 JPG/PNG/GIF/WebP 格式!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('上传图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 视频上传前验证
const beforeVideoUpload = (file) => {
  const isValidType = ['video/mp4', 'video/avi', 'video/mov'].includes(file.type)
  const isLt50M = file.size / 1024 / 1024 < 50

  if (!isValidType) {
    ElMessage.error('上传视频只能是 MP4/AVI/MOV 格式!')
    return false
  }
  if (!isLt50M) {
    ElMessage.error('上传视频大小不能超过 50MB!')
    return false
  }
  return true
}

// 背景图片上传
const handleImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const imageUrl = await uploadImage(file, (progress) => {
      loadingInstance.setText(`图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    // uploadImage 方法直接返回 URL 字符串
    form.backgroundImageUrl = imageUrl
    ElMessage.success('背景图片上传成功')
  } catch (error) {
    console.error('图片上传失败:', error)
    ElMessage.error(error.message || '图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 视频上传
const handleVideoUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '视频上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const videoUrl = await uploadVideo(file, (progress) => {
      loadingInstance.setText(`视频上传中... ${progress}%`)
    })

    loadingInstance.close()

    form.exampleVideoUrl = videoUrl
    ElMessage.success('视频上传成功')
  } catch (error) {
    console.error('视频上传失败:', error)
    ElMessage.error(error.message || '视频上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 地图图片上传
const handleMapUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '地图图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const imageUrl = await uploadImage(file, (progress) => {
      loadingInstance.setText(`地图图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    // uploadImage 方法直接返回 URL 字符串
    form.mapUrl = imageUrl
    ElMessage.success('地图图片上传成功')
  } catch (error) {
    console.error('地图图片上传失败:', error)
    ElMessage.error(error.message || '地图图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 收听图片上传
const handleListeningImageUpload = async (options) => {
  const { file } = options

  try {
    uploadLoading.value = true

    const loadingInstance = ElLoading.service({
      lock: true,
      text: '收听图片上传中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    const imageUrl = await uploadImage(file, (progress) => {
      loadingInstance.setText(`收听图片上传中... ${progress}%`)
    })

    loadingInstance.close()

    // uploadImage 方法直接返回 URL 字符串
    form.startListeningImageUrl = imageUrl
    ElMessage.success('收听图片上传成功')
  } catch (error) {
    console.error('收听图片上传失败:', error)
    ElMessage.error(error.message || '收听图片上传失败，请重试')
  } finally {
    uploadLoading.value = false
  }
}

// 获取详情数据
const fetchDetail = async () => {
  if (!isEdit.value) return

  try {
    const { data } = await getGuideProductById(route.params.id)
    Object.assign(form, data)
    
    // 如果有景区ID，需要加载对应的省份和城市数据
    if (data.scenicId) {
      await fetchProvinceList()
      // 这里需要根据景区ID获取对应的省份和城市信息
      // 暂时先不处理，等后端提供相关接口
    }
  } catch (error) {
    ElMessage.error('获取讲解产品详情失败')
    console.error(error)
  }
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    if (isEdit.value) {
      await updateGuideProduct(route.params.id, form)
      ElMessage.success('更新成功')
    } else {
      await createGuideProduct(form)
      ElMessage.success('创建成功')
    }

    handleBack()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
      console.error(error)
    }
  } finally {
    loading.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  if (isEdit.value) {
    fetchDetail()
  }
}

// 返回列表
const handleBack = () => {
  router.push('/guide-product/list')
}

// 初始化
onMounted(async () => {
  await fetchProvinceList()
  await fetchLecturerList()
  if (isEdit.value) {
    fetchDetail()
  }
})
</script>

<style scoped>
.guide-product-form {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.image-uploader .image {
  width: 300px;
  height: 100px;
  display: block;
}

.image-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.image-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.image-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 300px;
  height: 100px;
  text-align: center;
  line-height: 100px;
}

.image-uploader-loading {
  width: 300px;
  height: 100px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.image-uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.upload-tip {
  color: #999;
  font-size: 12px;
  margin-top: 5px;
}

.video-uploader .video-preview {
  width: 300px;
  height: 200px;
  display: block;
}

.video-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.video-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.uploader-placeholder {
  width: 300px;
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #8c939d;
  font-size: 14px;
}

.uploader-icon {
  font-size: 28px;
  margin-bottom: 8px;
}

.uploader-loading {
  width: 300px;
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #409eff;
  font-size: 14px;
}

.uploader-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.color-picker-container {
  display: flex;
  align-items: center;
}
</style>
