.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 固定顶部导航栏 */
.nav-bar-fixed {
  position: fixed;
  top: 0;
  left: 0;
  width: 750rpx;
  height: 100rpx;
  background-color: rgba(255,255,255,1.000000);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

/* 导航标签容器 */
.nav-tabs {
  width: 750rpx;
  height: 80rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 0 30rpx;
  box-sizing: border-box;
}

/* 单个导航标签 */
.nav-tab {
  flex: 1;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

/* 导航标签文字 */
.nav-tab-text {
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  color: rgba(0,0,0,0.400000);
  text-align: center;
  transition: all 0.3s ease;
}

/* 选中状态的导航标签文字 */
.nav-tab-text-active {
  color: rgb(0, 0, 0);
  font-weight: 500;
  font-family: PingFang SC-Medium;
}

/* 动态蓝色指示条 */
.indicator-bar {
  position: absolute;
  bottom: 0;
  width: 46rpx;
  height: 8rpx;
  background-color: rgba(64,128,255,1.000000);
  border-radius: 4rpx;
  transition: left 0.3s ease;
  margin-left: 76rpx; /* 居中调整 */
}

/* 内容区域 */
.content-area {
  margin-top: 100rpx; /* 为固定导航栏留出空间 */
  width: 750rpx;
  min-height: calc(100vh - 100rpx);
  display: flex;
  flex-direction: column;
}

/* 订单列表容器 */
.order-list {
  width: 690rpx;
  margin: 30rpx auto 0;
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400rpx;
}

.loading-text {
  font-size: 28rpx;
  color: rgba(0,0,0,0.6);
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  padding: 0 60rpx;
}

.error-text {
  font-size: 28rpx;
  color: rgba(0,0,0,0.6);
  text-align: center;
  margin-bottom: 40rpx;
}

.retry-button {
  background-color: rgba(64,128,255,1.000000);
  color: white;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
  border: none;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400rpx;
  padding: 0 60rpx;
}

.empty-text {
  font-size: 32rpx;
  color: rgba(0,0,0,0.8);
  margin-bottom: 20rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: rgba(0,0,0,0.5);
  text-align: center;
}
/* 订单卡片样式 */
.order-item {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  min-height: 330rpx;
  display: flex;
  flex-direction: column;
  margin-bottom: 20rpx;
}

/* 订单头部 */
.order-header {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}

/* 订单号 */
.order-no {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}

/* 订单状态 */
.order-status {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}

/* 已取消状态 */
.status-cancelled {
  color: rgba(134,144,156,1.000000);
}

/* 订单内容 */
.order-content {
  width: 612rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 44rpx 30rpx;
}

/* 产品图片 */
.product-image {
  width: 194rpx;
  height: 194rpx;
  border-radius: 8rpx;
}

/* 产品信息 */
.product-info {
  width: 394rpx;
  height: 176rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}

/* 产品名称 */
.product-name {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}

/* 下单时间 */
.order-time {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 22rpx;
}

/* 核销时间容器 */
.verify-time-wrapper {
  width: 394rpx;
  height: 34rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 10rpx;
}

/* 核销时间标签 */
.verify-label {
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}

/* 核销时间 */
.verify-time {
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}

/* 未核销状态 */
.verify-time.not-verified {
  color: rgba(238, 116, 53, 1);
}

/* 已核销状态 */
.verify-time.verified {
  color: rgba(0, 0, 0, 0.6);
}

/* 已取消状态 */
.verify-time.cancelled {
  color: rgba(134,144,156,1.000000);
}

/* 佣金容器 */
.commission-wrapper {
  width: 240rpx;
  height: 34rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 8rpx;
}

/* 佣金标签 */
.commission-label {
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}

/* 佣金符号 */
.commission-symbol {
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}

/* 佣金金额 */
.commission-amount {
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}


.box_5 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 330rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_7 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_23 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_24 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(64,128,255,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_4 {
  width: 612rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 44rpx 30rpx;
}
.image_5 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_4 {
  width: 394rpx;
  height: 176rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}
.text_25 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_26 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 22rpx;
}
.text-wrapper_8 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 10rpx;
}
.text_27 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_28 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(238, 116, 53, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_9 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 8rpx;
}
.text_29 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_30 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_31 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.box_6 {
  background-color: rgba(255,255,255,1.000000);
  border-radius: 16rpx;
  width: 690rpx;
  height: 330rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_10 {
  width: 630rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_32 {
  width: 372rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Light;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_33 {
  width: 84rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(134,144,156,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_5 {
  width: 612rpx;
  height: 194rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 28rpx 0 44rpx 30rpx;
}
.image_6 {
  width: 194rpx;
  height: 194rpx;
}
.text-group_5 {
  width: 394rpx;
  height: 176rpx;
  margin-top: 10rpx;
  display: flex;
  flex-direction: column;
}
.text_34 {
  width: 378rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_35 {
  width: 394rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 22rpx;
}
.text-wrapper_11 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 10rpx;
}
.text_36 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 0.6);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_37 {
  width: 204rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(238, 116, 53, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text-wrapper_12 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 8rpx;
}
.text_38 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_39 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_40 {
  width: 240rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.box_7 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 78rpx 0 38rpx 232rpx;
}