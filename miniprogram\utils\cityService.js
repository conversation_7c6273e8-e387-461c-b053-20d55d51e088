// 城市服务模块
const httpService = require('./httpService');

class CityService {
  constructor() {
    this.cacheKeyPrefix = 'cities_cache_';
    this.cacheTime = 5 * 60 * 1000; // 5分钟缓存
  }

  // 根据省份ID获取城市列表
  async getCitiesByProvince(provinceId) {
    try {
      if (!provinceId) {
        throw new Error('省份ID不能为空');
      }

      // 检查缓存
      const cachedData = this.getCache(provinceId);
      if (cachedData) {
        console.log(`使用缓存的城市数据 (省份ID: ${provinceId})`);
        return cachedData;
      }

      console.log(`从服务器获取城市数据 (省份ID: ${provinceId})`);
      const cities = await httpService.get('/api/cities', {
        provinceId: provinceId  // 使用 provinceId 参数名
      }, {
        loadingText: '加载城市中...'
      });

      // 缓存数据
      this.setCache(provinceId, cities);

      return cities;
    } catch (error) {
      console.error('获取城市列表失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 根据城市ID获取城市信息
  async getCityById(cityId) {
    try {
      if (!cityId) {
        throw new Error('城市ID不能为空');
      }

      const city = await httpService.get(`/api/cities/${cityId}`, {}, {
        loadingText: '加载中...'
      });

      return city;
    } catch (error) {
      console.error('获取城市信息失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 搜索城市
  async searchCities(keyword, provinceId = null) {
    try {
      if (!keyword) {
        return provinceId ? await this.getCitiesByProvince(provinceId) : [];
      }

      const params = { keyword };
      if (provinceId) {
        params.provinceId = provinceId;  // 使用 provinceId 参数名
      }

      const cities = await httpService.get('/api/cities/search', params, {
        loadingText: '搜索中...'
      });

      return cities;
    } catch (error) {
      console.error('搜索城市失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 获取热门城市
  async getHotCities(provinceId = null, limit = 10) {
    try {
      const params = { limit };
      if (provinceId) {
        params.provinceId = provinceId;  // 使用 provinceId 参数名
      }

      const cities = await httpService.get('/api/cities/hot', params, {
        showLoading: false
      });

      return cities;
    } catch (error) {
      console.error('获取热门城市失败:', error);
      // 热门城市失败时不显示错误提示
      throw error;
    }
  }

  // 获取所有城市（不分省份）
  async getAllCities() {
    try {
      const cities = await httpService.get('/api/cities/all', {}, {
        loadingText: '加载所有城市中...'
      });

      return cities;
    } catch (error) {
      console.error('获取所有城市失败:', error);
      httpService.handleError(error);
      throw error;
    }
  }

  // 缓存相关方法
  getCache(provinceId) {
    try {
      const cacheKey = `${this.cacheKeyPrefix}${provinceId}`;
      const cached = wx.getStorageSync(cacheKey);
      if (cached && cached.timestamp) {
        const now = Date.now();
        if (now - cached.timestamp < this.cacheTime) {
          return cached.data;
        } else {
          // 缓存过期，清除
          wx.removeStorageSync(cacheKey);
        }
      }
    } catch (error) {
      console.error('读取城市缓存失败:', error);
    }
    return null;
  }

  setCache(provinceId, data) {
    try {
      const cacheKey = `${this.cacheKeyPrefix}${provinceId}`;
      wx.setStorageSync(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('设置城市缓存失败:', error);
    }
  }

  clearCache(provinceId = null) {
    try {
      if (provinceId) {
        // 清除特定省份的城市缓存
        const cacheKey = `${this.cacheKeyPrefix}${provinceId}`;
        wx.removeStorageSync(cacheKey);
        console.log(`省份${provinceId}的城市缓存已清除`);
      } else {
        // 清除所有城市缓存
        const storage = wx.getStorageInfoSync();
        storage.keys.forEach(key => {
          if (key.startsWith(this.cacheKeyPrefix)) {
            wx.removeStorageSync(key);
          }
        });
        console.log('所有城市缓存已清除');
      }
    } catch (error) {
      console.error('清除城市缓存失败:', error);
    }
  }

  // 处理城市数据，添加"全部"选项
  processCitiesData(cities, includeAll = true) {
    const processedCities = Array.isArray(cities) ? [...cities] : [];

    if (includeAll) {
      processedCities.unshift({
        id: 0,
        name: '全部',
        code: 'ALL',
        provinceId: processedCities.length > 0 ? processedCities[0].provinceId : null
      });
    }

    return processedCities;
  }

  // 验证城市数据
  validateCity(city) {
    if (!city) {
      return false;
    }

    const requiredFields = ['id', 'name', 'provinceId'];
    return requiredFields.every(field => city.hasOwnProperty(field) && city[field] !== null && city[field] !== undefined);
  }

  // 格式化城市数据用于显示
  formatCityForDisplay(city) {
    if (!this.validateCity(city)) {
      return null;
    }

    return {
      id: city.id,
      name: city.name,
      code: city.code || '',
      provinceId: city.provinceId,
      displayName: city.name,
      sort: city.sort || 0,
      status: city.status || 1
    };
  }

  // 批量格式化城市数据
  formatCitiesForDisplay(cities, includeAll = true) {
    if (!Array.isArray(cities)) {
      return includeAll ? [{ id: 0, name: '全部', code: 'ALL' }] : [];
    }

    const formattedCities = cities
      .map(city => this.formatCityForDisplay(city))
      .filter(city => city !== null)
      .sort((a, b) => (a.sort || 0) - (b.sort || 0));

    return this.processCitiesData(formattedCities, includeAll);
  }

  // 根据城市名称查找城市
  findCityByName(cities, cityName) {
    if (!Array.isArray(cities) || !cityName) {
      return null;
    }

    return cities.find(city => city.name === cityName);
  }

  // 根据城市ID查找城市
  findCityById(cities, cityId) {
    if (!Array.isArray(cities) || cityId === null || cityId === undefined) {
      return null;
    }

    return cities.find(city => city.id === cityId);
  }
}

// 创建单例实例
const cityService = new CityService();

module.exports = cityService;
