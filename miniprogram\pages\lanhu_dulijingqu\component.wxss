/* 加载和错误状态样式 */
.loading-container, .error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #ffffff;
}

.loading-content, .error-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
}

.loading-text, .error-text {
  font-size: 32rpx;
  color: #666666;
  margin-bottom: 20rpx;
}

.retry-button {
  background-color: #007aff;
  color: #ffffff;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.content-container {
  width: 100%;
  height: auto;
  min-height: 100vh;
}

/* 景区图片轮播样式 */
.image-carousel {
  width: 100%;
  height: 400rpx;
}

.scenic-swiper {
  width: 100%;
  height: 100%;
}

.page {
  background-color: rgba(245,245,249,1.000000);
  position: relative;
  width: 750rpx;
  height: auto;
  display: flex;
  flex-direction: column;
}
.block_1 {
  background-color: rgba(255,255,255,1.000000);
  height: 176rpx;
  width: 750rpx;
  display: flex;
  flex-direction: column;
}
.image-wrapper_1 {
  width: 656rpx;
  height: 24rpx;
  flex-direction: row;
  display: flex;
  margin: 36rpx 0 0 48rpx;
}
.image_1 {
  width: 54rpx;
  height: 22rpx;
}
.thumbnail_1 {
  width: 34rpx;
  height: 22rpx;
  margin-left: 468rpx;
}
.thumbnail_2 {
  width: 32rpx;
  height: 24rpx;
  margin-left: 10rpx;
}
.image_2 {
  width: 48rpx;
  height: 22rpx;
  margin-left: 10rpx;
}
.section_1 {
  width: 416rpx;
  height: 40rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 50rpx 0 26rpx 38rpx;
}
.thumbnail_3 {
  width: 34rpx;
  height: 34rpx;
  margin-top: 6rpx;
}
.text_1 {
  width: 160rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  line-height: 34rpx;
}
.image_3 {
  width: 750rpx;
  height: 564rpx;
}
.block_2 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: 206rpx;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-center;
}
.text-group_1 {
  width: 670rpx;
  height: 86rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_2 {
  width: 488rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_3 {
  width: 670rpx;
  height: 32rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-top: 20rpx;
}
.section_2 {
  width: 626rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  margin: 26rpx 0 30rpx 30rpx;
}
.thumbnail_4 {
  width: 30rpx;
  height: 30rpx;
  margin-top: 2rpx;
}
.text_4 {
  width: 162rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 16rpx;
}
.thumbnail_5 {
  width: 32rpx;
  height: 32rpx;
  margin: 2rpx 0 0 38rpx;
}
.text_5 {
  width: 340rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 8rpx;
}
.block_3 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: auto;
  margin-top: -640rpx;
  display: flex;
  flex-direction: column;
}
.text_6 {
  width: 192rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 30rpx 0 0 30rpx;
}
.list_1 {
  width: 690rpx;
  height: auto;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin: 30rpx 0 26rpx 30rpx;
}
.list-items {
  background-color: rgba(232,243,255,1.000000);
  border-radius: 16rpx;
  height: 162rpx;
  border: 1px solid rgba(106,161,255,1);
  margin-bottom: 30rpx;
  width: 690rpx;
  display: flex;
  flex-direction: column;
}
.text-wrappers {
  width: 288rpx;
  height: 34rpx;
  display: flex;
  flex-direction: row;
  margin: 30rpx 0 0 30rpx;
}
.text_7 {
  width: 480rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.group_1 {
  width: 626rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  margin: 28rpx 0 36rpx 32rpx;
}
.thumbnail_6-0 {
  width: 20rpx;
  height: 28rpx;
  margin-top: 4rpx;
}
.text_8-0 {
  width: 54rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 12rpx;
}
.box_1-0 {
  background-color: rgba(64,128,255,0);
  width: 22rpx;
  height: 22rpx;
  display: flex;
  flex-direction: column;
  margin: 6rpx 0 0 30rpx;
}
.text_9-0 {
  width: 90rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 20rpx;
}
.text-wrapper_2-0 {
  width: 100rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  font-size: 0rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin-left: 278rpx;
}
.text_10-0 {
  width: 100rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_11-0 {
  width: 100rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(245, 63, 63, 1);
  font-size: 40rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.block_4 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: auto;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}
.text-wrapper_3 {
  width: 698rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  margin: 30rpx 0 0 30rpx;
}
.text_12 {
  width: 64rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 32rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_13 {
  width: 140rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.900000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.image-text_1 {
  width: 690rpx;
  min-height: 80rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin: 24rpx 0 20rpx 30rpx;
  padding-bottom: 20rpx;
}
.label_1 {
  width: 60rpx;
  height: 60rpx;
}
.text-group_2 {
  width: 600rpx;
  min-height: 60rpx;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
}
.text-wrapper_4 {
  width: 600rpx;
  height: 34rpx;
  flex-direction: row;
  display: flex;
  justify-content: space-between;
}
.text_14 {
  width: 112rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 28rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_15 {
  width: 140rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 24rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
}
.text_16 {
  width: 600rpx;
  min-height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,0.600000);
  font-size: 28rpx;
  font-family: PingFang SC-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 34rpx;
  margin-top: 8rpx;
}
.box_2 {
  background-color: rgba(0,0,0,1.000000);
  border-radius: 8rpx;
  width: 284rpx;
  height: 8rpx;
  display: flex;
  flex-direction: column;
  margin: 160rpx 0 0 232rpx;
}
.block_6 {
  background-color: rgba(255,255,255,1.000000);
  width: 750rpx;
  height: auto;
  margin-top: 20rpx;
  display: flex;
  flex-direction: column;
}
.text_17 {
  width: 144rpx;
  height: 34rpx;
  overflow-wrap: break-word;
  color: rgba(0,0,0,1.000000);
  font-size: 36rpx;
  font-family: PingFang SC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 34rpx;
  margin: 30rpx 0 0 30rpx;
}
.image_4 {
  width: 750rpx;
  height: auto;
  margin-top: 60rpx;
  display: block;
}

/* 无数据提示样式 */
.no-commentary-tips,
.no-detail-images {
  padding: 60rpx 30rpx;
  text-align: center;
}

.no-data-text {
  color: #999;
  font-size: 28rpx;
}
