const apiService = require('../../utils/apiService');

Page({
  data: {
    orderId: null,
    orderInfo: null,
    scenicList: [], // 需要评价的景区列表
    isBundle: false, // 是否为组合包订单
    loading: true,
    submitting: false,
    
    // 评价数据
    reviews: [], // 每个景区的评价数据
    
    // 用户信息
    userInfo: null,
    userId: null
  },

  onLoad: function(options) {
    console.log('评价页面加载，参数:', options);
    
    if (options.orderId) {
      this.setData({
        orderId: parseInt(options.orderId)
      });
      this.initPage();
    } else {
      wx.showToast({
        title: '订单ID缺失',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  // 初始化页面
  async initPage() {
    try {
      this.setData({ loading: true });

      // 获取用户信息
      await this.getUserInfo();
      
      // 获取订单信息
      await this.loadOrderInfo();
      
      // 加载景区信息
      await this.loadScenicInfo();
      
      // 初始化评价数据
      this.initReviewData();

    } catch (error) {
      console.error('初始化评价页面失败:', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    } finally {
      this.setData({ loading: false });
    }
  },

  // 获取用户信息
  async getUserInfo() {
    try {
      // 尝试多种存储key
      let storedUserInfo = wx.getStorageSync('userInfo');
      if (!storedUserInfo || !storedUserInfo.id) {
        storedUserInfo = wx.getStorageSync('user_info');
      }

      if (storedUserInfo && (storedUserInfo.id || storedUserInfo.userId)) {
        const userId = storedUserInfo.id || storedUserInfo.userId;
        this.setData({
          userInfo: storedUserInfo,
          userId: userId
        });
        console.log('用户信息获取成功:', { userId, nickname: storedUserInfo.nickname });
      } else {
        // 如果没有用户信息，使用默认用户ID（用于测试）
        console.warn('未找到用户信息，使用默认用户ID');
        this.setData({
          userInfo: { id: 1, nickname: '用户' },
          userId: 1
        });
      }
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 使用默认用户信息，不阻断流程
      this.setData({
        userInfo: { id: 1, nickname: '用户' },
        userId: 1
      });
    }
  },

  // 加载订单信息
  async loadOrderInfo() {
    try {
      // 这里需要根据实际API调整
      const orderDetail = await apiService.getOrderDetail(this.data.orderId);
      console.log('订单详情:', orderDetail);
      
      this.setData({
        orderInfo: orderDetail
      });
    } catch (error) {
      console.error('加载订单信息失败:', error);
      throw error;
    }
  },

  // 加载景区信息
  async loadScenicInfo() {
    try {
      // 获取订单关联的产品
      const products = await apiService.getProductsByOrderId(this.data.orderId);
      console.log('订单产品:', products);

      if (!products || products.length === 0) {
        throw new Error('订单没有关联的产品');
      }

      const scenicList = [];
      const firstProduct = products[0];
      
      if (firstProduct.productType === 'bundle') {
        // 组合包订单：解析多个景区ID
        this.setData({ isBundle: true });
        const scenicIds = firstProduct.scenicId.split(',');
        
        for (const scenicId of scenicIds) {
          try {
            const scenicDetail = await apiService.getScenicDetail(scenicId.trim());
            if (scenicDetail) {
              scenicList.push({
                scenicId: scenicId.trim(),
                title: scenicDetail.title || scenicDetail.name || '未知景区',
                image: scenicDetail.image || scenicDetail.images?.[0] || '',
                address: scenicDetail.address || '',
                description: scenicDetail.description || ''
              });
            }
          } catch (error) {
            console.error(`加载景区${scenicId}详情失败:`, error);
          }
        }
      } else {
        // 单景区订单
        this.setData({ isBundle: false });
        try {
          const scenicDetail = await apiService.getScenicDetail(firstProduct.scenicId);
          if (scenicDetail) {
            scenicList.push({
              scenicId: firstProduct.scenicId,
              title: scenicDetail.title || scenicDetail.name || '未知景区',
              image: scenicDetail.image || scenicDetail.images?.[0] || '',
              address: scenicDetail.address || '',
              description: scenicDetail.description || ''
            });
          }
        } catch (error) {
          console.error('加载景区详情失败:', error);
        }
      }

      this.setData({
        scenicList: scenicList
      });

      console.log('景区列表加载完成:', scenicList);
    } catch (error) {
      console.error('加载景区信息失败:', error);
      throw error;
    }
  },

  // 初始化评价数据
  initReviewData() {
    const reviews = this.data.scenicList.map(scenic => ({
      scenicId: scenic.scenicId,
      rating: 5, // 默认5星
      content: '', // 评价内容
      hasReviewed: false // 是否已评价
    }));

    this.setData({
      reviews: reviews
    });
  },

  // 星级评价点击
  onStarTap(e) {
    const { scenicIndex, rating } = e.currentTarget.dataset;
    const reviews = [...this.data.reviews];
    reviews[scenicIndex].rating = rating;
    
    this.setData({
      reviews: reviews
    });
  },

  // 评价内容输入
  onContentInput(e) {
    const { scenicIndex } = e.currentTarget.dataset;
    const content = e.detail.value;
    const reviews = [...this.data.reviews];
    reviews[scenicIndex].content = content;
    
    this.setData({
      reviews: reviews
    });
  },

  // 提交评价
  async submitReviews() {
    if (this.data.submitting) return;

    try {
      this.setData({ submitting: true });

      // 验证评价内容
      const emptyReviews = this.data.reviews.filter(review => !review.content.trim());
      if (emptyReviews.length > 0) {
        wx.showToast({
          title: '请填写所有景区的评价内容',
          icon: 'none'
        });
        return;
      }

      // 提交所有评价
      const submitPromises = this.data.reviews.map(review => this.submitSingleReview(review));
      await Promise.all(submitPromises);

      // 更新订单评价状态
      await this.updateOrderReviewStatus();

      wx.showToast({
        title: '评价提交成功',
        icon: 'success'
      });

      // 延迟返回订单列表
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);

    } catch (error) {
      console.error('提交评价失败:', error);
      wx.showToast({
        title: '提交失败，请重试',
        icon: 'none'
      });
    } finally {
      this.setData({ submitting: false });
    }
  },

  // 提交单个景区的评价
  async submitSingleReview(reviewData) {
    try {
      const reviewRequest = {
        userId: this.data.userId,
        scenicId: reviewData.scenicId,
        content: reviewData.content,
        rating: reviewData.rating, // 如果API支持评分
        status: 1, // 显示状态
        reviewTime: new Date().toISOString()
      };

      console.log('提交评价:', reviewRequest);
      
      const result = await this.callReviewAPI(reviewRequest);
      console.log('评价提交结果:', result);
      
      return result;
    } catch (error) {
      console.error('提交单个评价失败:', error);
      throw error;
    }
  },

  // 调用评价API
  async callReviewAPI(reviewData) {
    return new Promise((resolve, reject) => {
      const app = getApp();
      const baseUrl = app.globalData.baseUrl;

      wx.request({
        url: `${baseUrl}/api/reviews`,
        method: 'POST',
        header: {
          'Content-Type': 'application/json'
        },
        data: reviewData,
        success: (res) => {
          console.log('评价API响应:', res);
          if (res.statusCode === 200 && res.data.code === 200) {
            resolve(res.data.data);
          } else {
            reject(new Error(res.data.message || '评价提交失败'));
          }
        },
        fail: (error) => {
          console.error('评价API请求失败:', error);
          reject(error);
        }
      });
    });
  },

  // 更新订单评价状态
  async updateOrderReviewStatus() {
    try {
      console.log('更新订单评价状态，订单ID:', this.data.orderId);

      const app = getApp();
      const baseUrl = app.globalData.baseUrl;

      return new Promise((resolve, reject) => {
        wx.request({
          url: `${baseUrl}/api/orders/${this.data.orderId}/review-status`,
          method: 'PUT',
          data: {
            reviewStatus: 1 // 设置为已评价
          },
          header: {
            'Content-Type': 'application/x-www-form-urlencoded'
          },
          success: (res) => {
            console.log('更新订单评价状态API响应:', res);
            if (res.statusCode === 200 && res.data.code === 200) {
              console.log('订单评价状态更新成功');
              resolve(res.data);
            } else {
              console.error('更新订单评价状态失败:', res.data);
              // 不阻断流程，只记录错误
              resolve(null);
            }
          },
          fail: (error) => {
            console.error('更新订单评价状态请求失败:', error);
            // 不阻断流程，只记录错误
            resolve(null);
          }
        });
      });
    } catch (error) {
      console.error('更新订单评价状态异常:', error);
      // 不阻断流程，只记录错误
      return null;
    }
  },

  // 返回订单列表
  goBack() {
    wx.navigateBack();
  }
});
