package com.tourism.miniprogram.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.tourism.miniprogram.entity.Order;

import java.util.List;

/**
 * 订单服务接口
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
public interface OrderService extends IService<Order> {

    /**
     * 根据用户ID获取订单列表
     *
     * @param userId 用户ID
     * @return 订单列表
     */
    List<Order> getOrdersByUserId(Integer userId);

    /**
     * 根据状态获取订单列表
     *
     * @param status 订单状态
     * @return 订单列表
     */
    List<Order> getOrdersByStatus(String status);

    /**
     * 创建订单并生成门票
     *
     * @param order 订单信息
     * @return 是否成功
     */
    boolean createOrderWithCoupons(Order order);

    /**
     * 支付订单
     *
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean payOrder(Integer orderId);

    /**
     * 退款订单
     * 将订单状态置为canceled，并将对应的卡券状态置为expired
     *
     * @param orderId 订单ID
     * @return 是否成功
     */
    boolean refundOrder(Integer orderId);

    /**
     * 检查用户是否为首次下单
     *
     * @param userId 用户ID
     * @return 是否为首次下单
     */
    boolean isFirstOrder(Integer userId);

    /**
     * 根据订单号更新订单状态
     *
     * @param orderNo 订单号
     * @param status 订单状态
     * @return 是否成功
     */
    boolean updateOrderStatusByOrderNo(String orderNo, String status);

    /**
     * 根据订单号获取订单信息
     *
     * @param orderNo 订单号
     * @return 订单信息
     */
    Order getOrderByOrderNo(String orderNo);

    /**
     * 更新订单评价状态
     *
     * @param orderId 订单ID
     * @param reviewStatus 评价状态：0-未评价，1-已评价
     * @return 是否成功
     */
    boolean updateOrderReviewStatus(Integer orderId, Integer reviewStatus);

    /**
     * 根据订单号更新订单评价状态
     *
     * @param orderNo 订单号
     * @param reviewStatus 评价状态：0-未评价，1-已评价
     * @return 是否成功
     */
    boolean updateOrderReviewStatusByOrderNo(String orderNo, Integer reviewStatus);

    /**
     * 获取用户订单数量
     *
     * @param userId 用户ID
     * @return 订单数量
     */
    Integer getUserOrderCount(Integer userId);

    /**
     * 获取用户已支付订单数量
     *
     * @param userId 用户ID
     * @return 已支付订单数量
     */
    Integer getUserPaidOrderCount(Integer userId);
}
