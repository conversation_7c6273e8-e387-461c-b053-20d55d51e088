package com.tourism.miniprogram.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 讲师实体类
 *
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("lecturer")
@ApiModel(value = "Lecturer对象", description = "讲师信息")
public class Lecturer implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "讲师ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    @ApiModelProperty(value = "讲师姓名")
    @TableField("name")
    @NotBlank(message = "讲师姓名不能为空")
    private String name;

    @ApiModelProperty(value = "讲师头像URL")
    @TableField("avatar_url")
    @NotBlank(message = "讲师头像URL不能为空")
    private String avatarUrl;

    @ApiModelProperty(value = "头衔")
    @TableField("title")
    @NotBlank(message = "头衔不能为空")
    private String title;

    @ApiModelProperty(value = "讲师简介")
    @TableField("intro")
    @NotBlank(message = "讲师简介不能为空")
    private String intro;

    @ApiModelProperty(value = "专长领域")
    @TableField("expertise")
    private String expertise;

    @ApiModelProperty(value = "状态：1-启用，0-禁用")
    @TableField("status")
    private Integer status;

    @ApiModelProperty(value = "排序")
    @TableField("sort")
    private Integer sort;

    @ApiModelProperty(value = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @ApiModelProperty(value = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;
}
